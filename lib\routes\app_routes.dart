import 'package:flutter/material.dart';
import 'package:soul_reflector_app/features/chat/presentation/chat_screen.dart';
// Add imports for other screens when needed

class AppRoutes {
  static const String chat = '/';
  // static const String settings = '/settings';
  // static const String history = '/history';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case chat:
        return MaterialPageRoute(builder: (_) => const ChatScreen());
      // case settings:
      //   return MaterialPageRoute(builder: (_) => const SettingsScreen());
      // case history:
      //   return MaterialPageRoute(builder: (_) => const HistoryScreen());
      default:
        return MaterialPageRoute(
          builder:
              (_) => const Scaffold(body: Center(child: Text('Unknown route'))),
        );
    }
  }
}
