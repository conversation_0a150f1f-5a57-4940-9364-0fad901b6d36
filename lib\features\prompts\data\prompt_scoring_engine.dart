import 'package:soul_reflector_app/features/prompts/data/prompt_model.dart';

class PromptScoringEngine {
  /// Scores each prompt based on your priority:
  /// 1. intent (3 pts)
  /// 2. emotion (2 pts)
  /// 3. theme (2 pts)
  /// 4. semantic (future use)
  /// 5. keywords (0.5 pts)
  static PromptModel? getBestPromptFromList({
    required String userInput,
    required List<PromptModel> prompts,
  }) {
    PromptModel? bestPrompt;
    double highestScore = -1;

    for (final prompt in prompts) {
      final score = _calculateScore(userInput, prompt);
      if (score > highestScore) {
        highestScore = score;
        bestPrompt = prompt;
      }
    }

    return bestPrompt;
  }

  /// Calculates score for one prompt against user input
  static double _calculateScore(String input, PromptModel prompt) {
    final lowerInput = input.toLowerCase();
    double score = 0;

    // 1. Intent match (3 pts each match)
    score += _countMatches(prompt.intentTags, lowerInput) * 3;

    // 2. Emotion match (2 pts each match)
    score += _countMatches(prompt.emotionTags, lowerInput) * 2;

    // 3. Theme match (2 pts each match)
    score += _countMatches(prompt.themeTags, lowerInput) * 2;

    // 4. Semantic meaning — placeholder for future use with embeddings or NLP

    // 5. Keyword fallback (0.5 pt each match)
    score += _countMatches(prompt.keywords, lowerInput) * 0.5;

    return score;
  }

  /// Utility to count how many items from tags list appear in input string
  static int _countMatches(List<String> tags, String input) {
    return tags.where((tag) => input.contains(tag.toLowerCase())).length;
  }
}
