import 'dart:convert';
import 'dart:developer'; // ✅ Added for logging
import 'package:flutter/services.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_model.dart';

class PromptFolderLoader {
  /// Loads all prompts for a given style (e.g. funny, innerFlame)
  static Future<List<PromptModel>> loadPrompts(PromptStyle style) async {
    final fileName = style.folderFileName;
    final path =
        'assets/prompts/$fileName'; // Ensure this path is correct in pubspec.yaml

    try {
      final jsonString = await rootBundle.loadString(path);
      final List<dynamic> data = json.decode(jsonString);

      return data.map((e) => PromptModel.fromJson(e)).toList();
    } catch (e) {
      log(
        '❌ Failed to load prompts for $fileName: $e',
      ); // ✅ Replaced print with log
      return [];
    }
  }
}
