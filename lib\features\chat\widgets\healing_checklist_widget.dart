import 'package:flutter/material.dart';

class HealingChecklist extends StatelessWidget {
  final List<String> steps;
  final bool isVisible;

  const HealingChecklist({
    super.key, // Super parameter for key
    required this.steps,
    required this.isVisible,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible || steps.isEmpty) {
      return const SizedBox.shrink(); // Now wrapped in curly braces
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "🩺 Healing Suggestions",
            style: TextStyle(
              color: Colors.white70,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 6),
          ...steps.map(
            (item) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                children: [
                  const Icon(
                    Icons.arrow_right,
                    color: Colors.white54,
                    size: 20,
                  ),
                  Flexible(
                    child: Text(
                      item,
                      style: const TextStyle(
                        color: Colors.white60,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
