import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:provider/provider.dart';
import 'package:soul_reflector_app/core/constants/app_colors.dart';
import 'package:soul_reflector_app/core/services/tts_service.dart';
import 'package:soul_reflector_app/features/chat/application/chat_controller.dart';
import 'package:soul_reflector_app/features/chat/data/chat_repository.dart';
import 'package:soul_reflector_app/models/message_model.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';
import 'package:soul_reflector_app/features/chat/widgets/healing_checklist_widget.dart';

class ChatScreen extends StatelessWidget {
  const ChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ChatController(ChatRepository()),
      child: const ChatView(),
    );
  }
}

class ChatView extends StatefulWidget {
  const ChatView({super.key});

  @override
  State<ChatView> createState() => _ChatViewState();
}

class _ChatViewState extends State<ChatView> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _inputController = TextEditingController();
  bool showChecklist = false;

  @override
  void dispose() {
    _inputController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Provider.of<ChatController>(context);
    final hasInput = _inputController.text.trim().isNotEmpty;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Soul Reflector'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(child: _buildMessageList(controller)),
            const Divider(height: 1, color: Colors.grey),
            _buildInputArea(controller, hasInput),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageList(ChatController controller) {
    final messages = controller.messages;
    final suggestions = controller.suggestions;
    final checklist = controller.matchedPrompt?.healingChecklist ?? [];

    return ListView(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      children: [
        ...messages.map(_buildMessageBubble),
        if (controller.isLoading) _buildLoadingIndicator(),
        if (suggestions.isNotEmpty) _buildSuggestions(suggestions, controller),
        if (checklist.isNotEmpty)
          TextButton(
            onPressed: () => setState(() => showChecklist = !showChecklist),
            child: Text(
              showChecklist ? "Hide Healing Steps" : "💡 Show Healing Steps",
              style: const TextStyle(color: AppColors.accent),
            ),
          ),
        if (checklist.isNotEmpty)
          HealingChecklist(steps: checklist, isVisible: showChecklist),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildMessageBubble(MessageModel message) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6),
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: message.isUser ? AppColors.userBubble : AppColors.aiBubble,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child:
                  message.isUser
                      ? Text(
                        message.text,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.5,
                          color: AppColors.userText,
                        ),
                      )
                      : MarkdownBody(
                        data: message.text,
                        styleSheet: MarkdownStyleSheet.fromTheme(
                          Theme.of(context),
                        ),
                      ),
            ),
            if (!message.isUser)
              IconButton(
                icon: const Icon(
                  Icons.volume_up_rounded,
                  size: 20,
                  color: AppColors.accent,
                ),
                onPressed: () => TtsService.speak(message.text),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 10),
      child: Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildSuggestions(
    List<String> suggestions,
    ChatController controller,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: Wrap(
        spacing: 10,
        runSpacing: 8,
        children:
            suggestions.map((suggestion) {
              return ActionChip(
                backgroundColor: AppColors.suggestionChip,
                labelStyle: const TextStyle(color: Colors.white),
                label: Text(suggestion),
                onPressed: () {
                  controller.sendMessage(suggestion);
                  _scrollToBottom();
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildInputArea(ChatController controller, bool hasInput) {
    final toneChips = {
      PromptStyle.truthBomb: "💣 Truth Bomb",
      PromptStyle.innerFlame: "🔥 Inner Flame",
      PromptStyle.mindMirror: "🧠 Mind Mirror",
      PromptStyle.wisdomWhisper: "🌬️ Wisdom Whisper",
      PromptStyle.mysticLaughter: "😂 Mystic Laughter",
      PromptStyle.innerDoctor: "🌿 Inner Doctor",
      PromptStyle.knowYourself: "🔍 Know Yourself",
    };

    return Container(
      color: AppColors.surface,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "🎛️ Choose how you'd like this answered:",
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
          const SizedBox(height: 6),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                toneChips.entries.map((entry) {
                  final isSelected = controller.selectedStyle == entry.key;
                  return ChoiceChip(
                    label: Text(entry.value),
                    selected: isSelected,
                    onSelected: (_) => controller.selectStyle(entry.key),
                    selectedColor: AppColors.accent,
                    labelStyle: TextStyle(
                      color: isSelected ? Colors.white : Colors.white70,
                    ),
                    backgroundColor: Colors.black54,
                  );
                }).toList(),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _inputController,
                  style: const TextStyle(color: Colors.white),
                  onChanged: (_) => setState(() {}),
                  onSubmitted: (_) => _send(controller),
                  decoration: InputDecoration(
                    hintText: "Ask something deep...",
                    hintStyle: const TextStyle(color: Colors.white60),
                    filled: true,
                    fillColor: AppColors.inputField,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 14,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: Icon(
                  Icons.send,
                  color: hasInput ? Colors.white : Colors.grey,
                ),
                onPressed: hasInput ? () => _send(controller) : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _send(ChatController controller) {
    final text = _inputController.text.trim();
    if (text.isEmpty) return;

    controller.sendMessage(text);
    _inputController.clear();
    setState(() {});
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent + 100,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
}
