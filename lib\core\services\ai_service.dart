import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AiService {
  static const String _baseUrl = 'https://api.openai.com/v1/chat/completions';

  static Future<String> getReflectiveResponse({
    required String userInput,
    required String systemPrompt,
    String model = 'gpt-4o',
    double temperature = 0.9,
    int maxTokens = 4000,
  }) async {
    final apiKey = dotenv.env['OPENAI_API_KEY'];

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception("❌ Missing OpenAI API key in .env file");
    }

    final headers = {
      'Authorization': 'Bearer $apiKey',
      'Content-Type': 'application/json',
    };

    final body = jsonEncode({
      "model": model,
      "temperature": temperature,
      "max_tokens": maxTokens,
      "top_p": 1.0,
      "presence_penalty": 0.6,
      "frequency_penalty": 0.3,
      "messages": [
        {"role": "system", "content": systemPrompt},
        {"role": "user", "content": userInput},
      ],
    });

    try {
      final response = await http
          .post(Uri.parse(_baseUrl), headers: headers, body: body)
          .timeout(const Duration(seconds: 20));

      if (response.statusCode == 200) {
        final json = jsonDecode(utf8.decode(response.bodyBytes));
        final reply = json['choices'][0]['message']['content'];
        return reply.trim();
      } else {
        log("⚠️ GPT Error: ${response.statusCode} → ${response.body}");
        throw Exception("GPT response failed: ${response.statusCode}");
      }
    } catch (e) {
      log("❌ AI Service Error: $e");
      return "⚠️ I wasn’t able to reflect deeply right now. Please try again.";
    }
  }
}
