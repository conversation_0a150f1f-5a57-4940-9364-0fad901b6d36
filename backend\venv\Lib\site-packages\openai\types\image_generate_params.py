# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union, Optional
from typing_extensions import Literal, Required, TypedDict

from .image_model import ImageModel

__all__ = ["ImageGenerateParams"]


class ImageGenerateParams(TypedDict, total=False):
    prompt: Required[str]
    """A text description of the desired image(s).

    The maximum length is 32000 characters for `gpt-image-1`, 1000 characters for
    `dall-e-2` and 4000 characters for `dall-e-3`.
    """

    background: Optional[Literal["transparent", "opaque", "auto"]]
    """Allows to set transparency for the background of the generated image(s).

    This parameter is only supported for `gpt-image-1`. Must be one of
    `transparent`, `opaque` or `auto` (default value). When `auto` is used, the
    model will automatically determine the best background for the image.

    If `transparent`, the output format needs to support transparency, so it should
    be set to either `png` (default value) or `webp`.
    """

    model: Union[str, ImageModel, None]
    """The model to use for image generation.

    One of `dall-e-2`, `dall-e-3`, or `gpt-image-1`. Defaults to `dall-e-2` unless a
    parameter specific to `gpt-image-1` is used.
    """

    moderation: Optional[Literal["low", "auto"]]
    """Control the content-moderation level for images generated by `gpt-image-1`.

    Must be either `low` for less restrictive filtering or `auto` (default value).
    """

    n: Optional[int]
    """The number of images to generate.

    Must be between 1 and 10. For `dall-e-3`, only `n=1` is supported.
    """

    output_compression: Optional[int]
    """The compression level (0-100%) for the generated images.

    This parameter is only supported for `gpt-image-1` with the `webp` or `jpeg`
    output formats, and defaults to 100.
    """

    output_format: Optional[Literal["png", "jpeg", "webp"]]
    """The format in which the generated images are returned.

    This parameter is only supported for `gpt-image-1`. Must be one of `png`,
    `jpeg`, or `webp`.
    """

    quality: Optional[Literal["standard", "hd", "low", "medium", "high", "auto"]]
    """The quality of the image that will be generated.

    - `auto` (default value) will automatically select the best quality for the
      given model.
    - `high`, `medium` and `low` are supported for `gpt-image-1`.
    - `hd` and `standard` are supported for `dall-e-3`.
    - `standard` is the only option for `dall-e-2`.
    """

    response_format: Optional[Literal["url", "b64_json"]]
    """The format in which generated images with `dall-e-2` and `dall-e-3` are
    returned.

    Must be one of `url` or `b64_json`. URLs are only valid for 60 minutes after the
    image has been generated. This parameter isn't supported for `gpt-image-1` which
    will always return base64-encoded images.
    """

    size: Optional[
        Literal["auto", "1024x1024", "1536x1024", "1024x1536", "256x256", "512x512", "1792x1024", "1024x1792"]
    ]
    """The size of the generated images.

    Must be one of `1024x1024`, `1536x1024` (landscape), `1024x1536` (portrait), or
    `auto` (default value) for `gpt-image-1`, one of `256x256`, `512x512`, or
    `1024x1024` for `dall-e-2`, and one of `1024x1024`, `1792x1024`, or `1024x1792`
    for `dall-e-3`.
    """

    style: Optional[Literal["vivid", "natural"]]
    """The style of the generated images.

    This parameter is only supported for `dall-e-3`. Must be one of `vivid` or
    `natural`. Vivid causes the model to lean towards generating hyper-real and
    dramatic images. Natural causes the model to produce more natural, less
    hyper-real looking images.
    """

    user: str
    """
    A unique identifier representing your end-user, which can help OpenAI to monitor
    and detect abuse.
    [Learn more](https://platform.openai.com/docs/guides/safety-best-practices#end-user-ids).
    """
