import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:soul_reflector_app/core/env/env_config.dart';
import 'package:soul_reflector_app/models/message_model.dart';

class ApiService {
  final String _apiKey = EnvConfig.apiKey;

  final Uri _chatEndpoint = Uri.parse(
    "https://api.openai.com/v1/chat/completions",
  );

  Future<MessageModel> sendMessage({
    required List<MessageModel> messages,
    required String systemPrompt,
    double temperature = 0.9,
    int maxTokens = 4000,
  }) async {
    try {
      final headers = {
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      };

      final payload = {
        'model': 'gpt-4o',
        'temperature': temperature,
        'top_p': 1.0,
        'presence_penalty': 0.5,
        'frequency_penalty': 0.1,
        'max_tokens': maxTokens,
        'messages': _formatMessagesWithSystemPrompt(systemPrompt, messages),
      };

      final response = await http
          .post(_chatEndpoint, headers: headers, body: jsonEncode(payload))
          .timeout(const Duration(seconds: 20));

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        final content = data['choices'][0]['message']['content'] ?? '';
        return MessageModel(role: 'assistant', content: content.trim());
      } else {
        throw Exception("GPT Error: ${response.statusCode} → ${response.body}");
      }
    } catch (e) {
      throw Exception("API call failed: $e");
    }
  }

  /// Injects system prompt and full message history
  List<Map<String, String>> _formatMessagesWithSystemPrompt(
    String systemPrompt,
    List<MessageModel> messages,
  ) {
    final formatted = <Map<String, String>>[];

    if (systemPrompt.isNotEmpty) {
      formatted.add({"role": "system", "content": systemPrompt});
    }

    for (final msg in messages) {
      formatted.add({"role": msg.role, "content": msg.content});
    }

    return formatted;
  }
}
