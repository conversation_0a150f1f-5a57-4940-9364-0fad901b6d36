import 'package:shared_preferences/shared_preferences.dart';

class UserPreferences {
  static SharedPreferences? _prefs;

  static const _darkModeKey = 'dark_mode';
  static const _fontSizeKey = 'font_size';

  /// Init preferences in main()
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// DARK MODE
  static bool? get isDarkMode => _prefs?.getBool(_darkModeKey);
  static Future<void> setDarkMode(bool value) async {
    await _prefs?.setBool(_darkModeKey, value);
  }

  /// FONT SIZE (future upgrade)
  static double get fontSize => _prefs?.getDouble(_fontSizeKey) ?? 16.0;
  static Future<void> setFontSize(double value) async {
    await _prefs?.setDouble(_fontSizeKey, value);
  }

  /// Reset all settings (if needed)
  static Future<void> reset() async {
    await _prefs?.clear();
  }
}
