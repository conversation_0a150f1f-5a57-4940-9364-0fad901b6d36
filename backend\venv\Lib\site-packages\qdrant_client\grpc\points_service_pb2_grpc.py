# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import points_pb2 as points__pb2


class PointsStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Upsert = channel.unary_unary(
                '/qdrant.Points/Upsert',
                request_serializer=points__pb2.UpsertPoints.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.Delete = channel.unary_unary(
                '/qdrant.Points/Delete',
                request_serializer=points__pb2.DeletePoints.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.Get = channel.unary_unary(
                '/qdrant.Points/Get',
                request_serializer=points__pb2.GetPoints.SerializeToString,
                response_deserializer=points__pb2.GetResponse.FromString,
                )
        self.UpdateVectors = channel.unary_unary(
                '/qdrant.Points/UpdateVectors',
                request_serializer=points__pb2.UpdatePointVectors.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.DeleteVectors = channel.unary_unary(
                '/qdrant.Points/DeleteVectors',
                request_serializer=points__pb2.DeletePointVectors.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.SetPayload = channel.unary_unary(
                '/qdrant.Points/SetPayload',
                request_serializer=points__pb2.SetPayloadPoints.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.OverwritePayload = channel.unary_unary(
                '/qdrant.Points/OverwritePayload',
                request_serializer=points__pb2.SetPayloadPoints.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.DeletePayload = channel.unary_unary(
                '/qdrant.Points/DeletePayload',
                request_serializer=points__pb2.DeletePayloadPoints.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.ClearPayload = channel.unary_unary(
                '/qdrant.Points/ClearPayload',
                request_serializer=points__pb2.ClearPayloadPoints.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.CreateFieldIndex = channel.unary_unary(
                '/qdrant.Points/CreateFieldIndex',
                request_serializer=points__pb2.CreateFieldIndexCollection.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.DeleteFieldIndex = channel.unary_unary(
                '/qdrant.Points/DeleteFieldIndex',
                request_serializer=points__pb2.DeleteFieldIndexCollection.SerializeToString,
                response_deserializer=points__pb2.PointsOperationResponse.FromString,
                )
        self.Search = channel.unary_unary(
                '/qdrant.Points/Search',
                request_serializer=points__pb2.SearchPoints.SerializeToString,
                response_deserializer=points__pb2.SearchResponse.FromString,
                )
        self.SearchBatch = channel.unary_unary(
                '/qdrant.Points/SearchBatch',
                request_serializer=points__pb2.SearchBatchPoints.SerializeToString,
                response_deserializer=points__pb2.SearchBatchResponse.FromString,
                )
        self.SearchGroups = channel.unary_unary(
                '/qdrant.Points/SearchGroups',
                request_serializer=points__pb2.SearchPointGroups.SerializeToString,
                response_deserializer=points__pb2.SearchGroupsResponse.FromString,
                )
        self.Scroll = channel.unary_unary(
                '/qdrant.Points/Scroll',
                request_serializer=points__pb2.ScrollPoints.SerializeToString,
                response_deserializer=points__pb2.ScrollResponse.FromString,
                )
        self.Recommend = channel.unary_unary(
                '/qdrant.Points/Recommend',
                request_serializer=points__pb2.RecommendPoints.SerializeToString,
                response_deserializer=points__pb2.RecommendResponse.FromString,
                )
        self.RecommendBatch = channel.unary_unary(
                '/qdrant.Points/RecommendBatch',
                request_serializer=points__pb2.RecommendBatchPoints.SerializeToString,
                response_deserializer=points__pb2.RecommendBatchResponse.FromString,
                )
        self.RecommendGroups = channel.unary_unary(
                '/qdrant.Points/RecommendGroups',
                request_serializer=points__pb2.RecommendPointGroups.SerializeToString,
                response_deserializer=points__pb2.RecommendGroupsResponse.FromString,
                )
        self.Discover = channel.unary_unary(
                '/qdrant.Points/Discover',
                request_serializer=points__pb2.DiscoverPoints.SerializeToString,
                response_deserializer=points__pb2.DiscoverResponse.FromString,
                )
        self.DiscoverBatch = channel.unary_unary(
                '/qdrant.Points/DiscoverBatch',
                request_serializer=points__pb2.DiscoverBatchPoints.SerializeToString,
                response_deserializer=points__pb2.DiscoverBatchResponse.FromString,
                )
        self.Count = channel.unary_unary(
                '/qdrant.Points/Count',
                request_serializer=points__pb2.CountPoints.SerializeToString,
                response_deserializer=points__pb2.CountResponse.FromString,
                )
        self.UpdateBatch = channel.unary_unary(
                '/qdrant.Points/UpdateBatch',
                request_serializer=points__pb2.UpdateBatchPoints.SerializeToString,
                response_deserializer=points__pb2.UpdateBatchResponse.FromString,
                )
        self.Query = channel.unary_unary(
                '/qdrant.Points/Query',
                request_serializer=points__pb2.QueryPoints.SerializeToString,
                response_deserializer=points__pb2.QueryResponse.FromString,
                )
        self.QueryBatch = channel.unary_unary(
                '/qdrant.Points/QueryBatch',
                request_serializer=points__pb2.QueryBatchPoints.SerializeToString,
                response_deserializer=points__pb2.QueryBatchResponse.FromString,
                )
        self.QueryGroups = channel.unary_unary(
                '/qdrant.Points/QueryGroups',
                request_serializer=points__pb2.QueryPointGroups.SerializeToString,
                response_deserializer=points__pb2.QueryGroupsResponse.FromString,
                )
        self.Facet = channel.unary_unary(
                '/qdrant.Points/Facet',
                request_serializer=points__pb2.FacetCounts.SerializeToString,
                response_deserializer=points__pb2.FacetResponse.FromString,
                )
        self.SearchMatrixPairs = channel.unary_unary(
                '/qdrant.Points/SearchMatrixPairs',
                request_serializer=points__pb2.SearchMatrixPoints.SerializeToString,
                response_deserializer=points__pb2.SearchMatrixPairsResponse.FromString,
                )
        self.SearchMatrixOffsets = channel.unary_unary(
                '/qdrant.Points/SearchMatrixOffsets',
                request_serializer=points__pb2.SearchMatrixPoints.SerializeToString,
                response_deserializer=points__pb2.SearchMatrixOffsetsResponse.FromString,
                )


class PointsServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Upsert(self, request, context):
        """
        Perform insert + updates on points. If a point with a given ID already exists - it will be overwritten.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Delete(self, request, context):
        """
        Delete points
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Get(self, request, context):
        """
        Retrieve points
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateVectors(self, request, context):
        """
        Update named vectors for point
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteVectors(self, request, context):
        """
        Delete named vectors for points
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetPayload(self, request, context):
        """
        Set payload for points
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OverwritePayload(self, request, context):
        """
        Overwrite payload for points
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePayload(self, request, context):
        """
        Delete specified key payload for points
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearPayload(self, request, context):
        """
        Remove all payload for specified points
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateFieldIndex(self, request, context):
        """
        Create index for field in collection
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteFieldIndex(self, request, context):
        """
        Delete field index for collection
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Search(self, request, context):
        """
        Retrieve closest points based on vector similarity and given filtering conditions
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchBatch(self, request, context):
        """
        Retrieve closest points based on vector similarity and given filtering conditions
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchGroups(self, request, context):
        """
        Retrieve closest points based on vector similarity and given filtering conditions, grouped by a given field
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Scroll(self, request, context):
        """
        Iterate over all or filtered points
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Recommend(self, request, context):
        """
        Look for the points which are closer to stored positive examples and at the same time further to negative examples.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RecommendBatch(self, request, context):
        """
        Look for the points which are closer to stored positive examples and at the same time further to negative examples.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RecommendGroups(self, request, context):
        """
        Look for the points which are closer to stored positive examples and at the same time further to negative examples, grouped by a given field
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Discover(self, request, context):
        """
        Use context and a target to find the most similar points to the target, constrained by the context.

        When using only the context (without a target), a special search - called context search - is performed where
        pairs of points are used to generate a loss that guides the search towards the zone where
        most positive examples overlap. This means that the score minimizes the scenario of
        finding a point closer to a negative than to a positive part of a pair.

        Since the score of a context relates to loss, the maximum score a point can get is 0.0,
        and it becomes normal that many points can have a score of 0.0.

        When using target (with or without context), the score behaves a little different: The 
        integer part of the score represents the rank with respect to the context, while the
        decimal part of the score relates to the distance to the target. The context part of the score for 
        each pair is calculated +1 if the point is closer to a positive than to a negative part of a pair, 
        and -1 otherwise.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DiscoverBatch(self, request, context):
        """
        Batch request points based on { positive, negative } pairs of examples, and/or a target
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Count(self, request, context):
        """
        Count points in collection with given filtering conditions
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateBatch(self, request, context):
        """
        Perform multiple update operations in one request
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Query(self, request, context):
        """
        Universally query points. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueryBatch(self, request, context):
        """
        Universally query points in a batch fashion. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueryGroups(self, request, context):
        """
        Universally query points in a group fashion. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Facet(self, request, context):
        """
        Perform facet counts. For each value in the field, count the number of points that have this value and match the conditions.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchMatrixPairs(self, request, context):
        """
        Compute distance matrix for sampled points with a pair based output format
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchMatrixOffsets(self, request, context):
        """
        Compute distance matrix for sampled points with an offset based output format
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PointsServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Upsert': grpc.unary_unary_rpc_method_handler(
                    servicer.Upsert,
                    request_deserializer=points__pb2.UpsertPoints.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'Delete': grpc.unary_unary_rpc_method_handler(
                    servicer.Delete,
                    request_deserializer=points__pb2.DeletePoints.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'Get': grpc.unary_unary_rpc_method_handler(
                    servicer.Get,
                    request_deserializer=points__pb2.GetPoints.FromString,
                    response_serializer=points__pb2.GetResponse.SerializeToString,
            ),
            'UpdateVectors': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateVectors,
                    request_deserializer=points__pb2.UpdatePointVectors.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'DeleteVectors': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteVectors,
                    request_deserializer=points__pb2.DeletePointVectors.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'SetPayload': grpc.unary_unary_rpc_method_handler(
                    servicer.SetPayload,
                    request_deserializer=points__pb2.SetPayloadPoints.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'OverwritePayload': grpc.unary_unary_rpc_method_handler(
                    servicer.OverwritePayload,
                    request_deserializer=points__pb2.SetPayloadPoints.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'DeletePayload': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePayload,
                    request_deserializer=points__pb2.DeletePayloadPoints.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'ClearPayload': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearPayload,
                    request_deserializer=points__pb2.ClearPayloadPoints.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'CreateFieldIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateFieldIndex,
                    request_deserializer=points__pb2.CreateFieldIndexCollection.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'DeleteFieldIndex': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteFieldIndex,
                    request_deserializer=points__pb2.DeleteFieldIndexCollection.FromString,
                    response_serializer=points__pb2.PointsOperationResponse.SerializeToString,
            ),
            'Search': grpc.unary_unary_rpc_method_handler(
                    servicer.Search,
                    request_deserializer=points__pb2.SearchPoints.FromString,
                    response_serializer=points__pb2.SearchResponse.SerializeToString,
            ),
            'SearchBatch': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchBatch,
                    request_deserializer=points__pb2.SearchBatchPoints.FromString,
                    response_serializer=points__pb2.SearchBatchResponse.SerializeToString,
            ),
            'SearchGroups': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchGroups,
                    request_deserializer=points__pb2.SearchPointGroups.FromString,
                    response_serializer=points__pb2.SearchGroupsResponse.SerializeToString,
            ),
            'Scroll': grpc.unary_unary_rpc_method_handler(
                    servicer.Scroll,
                    request_deserializer=points__pb2.ScrollPoints.FromString,
                    response_serializer=points__pb2.ScrollResponse.SerializeToString,
            ),
            'Recommend': grpc.unary_unary_rpc_method_handler(
                    servicer.Recommend,
                    request_deserializer=points__pb2.RecommendPoints.FromString,
                    response_serializer=points__pb2.RecommendResponse.SerializeToString,
            ),
            'RecommendBatch': grpc.unary_unary_rpc_method_handler(
                    servicer.RecommendBatch,
                    request_deserializer=points__pb2.RecommendBatchPoints.FromString,
                    response_serializer=points__pb2.RecommendBatchResponse.SerializeToString,
            ),
            'RecommendGroups': grpc.unary_unary_rpc_method_handler(
                    servicer.RecommendGroups,
                    request_deserializer=points__pb2.RecommendPointGroups.FromString,
                    response_serializer=points__pb2.RecommendGroupsResponse.SerializeToString,
            ),
            'Discover': grpc.unary_unary_rpc_method_handler(
                    servicer.Discover,
                    request_deserializer=points__pb2.DiscoverPoints.FromString,
                    response_serializer=points__pb2.DiscoverResponse.SerializeToString,
            ),
            'DiscoverBatch': grpc.unary_unary_rpc_method_handler(
                    servicer.DiscoverBatch,
                    request_deserializer=points__pb2.DiscoverBatchPoints.FromString,
                    response_serializer=points__pb2.DiscoverBatchResponse.SerializeToString,
            ),
            'Count': grpc.unary_unary_rpc_method_handler(
                    servicer.Count,
                    request_deserializer=points__pb2.CountPoints.FromString,
                    response_serializer=points__pb2.CountResponse.SerializeToString,
            ),
            'UpdateBatch': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateBatch,
                    request_deserializer=points__pb2.UpdateBatchPoints.FromString,
                    response_serializer=points__pb2.UpdateBatchResponse.SerializeToString,
            ),
            'Query': grpc.unary_unary_rpc_method_handler(
                    servicer.Query,
                    request_deserializer=points__pb2.QueryPoints.FromString,
                    response_serializer=points__pb2.QueryResponse.SerializeToString,
            ),
            'QueryBatch': grpc.unary_unary_rpc_method_handler(
                    servicer.QueryBatch,
                    request_deserializer=points__pb2.QueryBatchPoints.FromString,
                    response_serializer=points__pb2.QueryBatchResponse.SerializeToString,
            ),
            'QueryGroups': grpc.unary_unary_rpc_method_handler(
                    servicer.QueryGroups,
                    request_deserializer=points__pb2.QueryPointGroups.FromString,
                    response_serializer=points__pb2.QueryGroupsResponse.SerializeToString,
            ),
            'Facet': grpc.unary_unary_rpc_method_handler(
                    servicer.Facet,
                    request_deserializer=points__pb2.FacetCounts.FromString,
                    response_serializer=points__pb2.FacetResponse.SerializeToString,
            ),
            'SearchMatrixPairs': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchMatrixPairs,
                    request_deserializer=points__pb2.SearchMatrixPoints.FromString,
                    response_serializer=points__pb2.SearchMatrixPairsResponse.SerializeToString,
            ),
            'SearchMatrixOffsets': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchMatrixOffsets,
                    request_deserializer=points__pb2.SearchMatrixPoints.FromString,
                    response_serializer=points__pb2.SearchMatrixOffsetsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'qdrant.Points', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Points(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Upsert(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Upsert',
            points__pb2.UpsertPoints.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Delete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Delete',
            points__pb2.DeletePoints.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Get(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Get',
            points__pb2.GetPoints.SerializeToString,
            points__pb2.GetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateVectors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/UpdateVectors',
            points__pb2.UpdatePointVectors.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteVectors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/DeleteVectors',
            points__pb2.DeletePointVectors.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetPayload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/SetPayload',
            points__pb2.SetPayloadPoints.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def OverwritePayload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/OverwritePayload',
            points__pb2.SetPayloadPoints.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeletePayload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/DeletePayload',
            points__pb2.DeletePayloadPoints.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClearPayload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/ClearPayload',
            points__pb2.ClearPayloadPoints.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CreateFieldIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/CreateFieldIndex',
            points__pb2.CreateFieldIndexCollection.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteFieldIndex(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/DeleteFieldIndex',
            points__pb2.DeleteFieldIndexCollection.SerializeToString,
            points__pb2.PointsOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Search(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Search',
            points__pb2.SearchPoints.SerializeToString,
            points__pb2.SearchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SearchBatch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/SearchBatch',
            points__pb2.SearchBatchPoints.SerializeToString,
            points__pb2.SearchBatchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SearchGroups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/SearchGroups',
            points__pb2.SearchPointGroups.SerializeToString,
            points__pb2.SearchGroupsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Scroll(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Scroll',
            points__pb2.ScrollPoints.SerializeToString,
            points__pb2.ScrollResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Recommend(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Recommend',
            points__pb2.RecommendPoints.SerializeToString,
            points__pb2.RecommendResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RecommendBatch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/RecommendBatch',
            points__pb2.RecommendBatchPoints.SerializeToString,
            points__pb2.RecommendBatchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RecommendGroups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/RecommendGroups',
            points__pb2.RecommendPointGroups.SerializeToString,
            points__pb2.RecommendGroupsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Discover(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Discover',
            points__pb2.DiscoverPoints.SerializeToString,
            points__pb2.DiscoverResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DiscoverBatch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/DiscoverBatch',
            points__pb2.DiscoverBatchPoints.SerializeToString,
            points__pb2.DiscoverBatchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Count(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Count',
            points__pb2.CountPoints.SerializeToString,
            points__pb2.CountResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateBatch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/UpdateBatch',
            points__pb2.UpdateBatchPoints.SerializeToString,
            points__pb2.UpdateBatchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Query(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Query',
            points__pb2.QueryPoints.SerializeToString,
            points__pb2.QueryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueryBatch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/QueryBatch',
            points__pb2.QueryBatchPoints.SerializeToString,
            points__pb2.QueryBatchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueryGroups(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/QueryGroups',
            points__pb2.QueryPointGroups.SerializeToString,
            points__pb2.QueryGroupsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Facet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/Facet',
            points__pb2.FacetCounts.SerializeToString,
            points__pb2.FacetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SearchMatrixPairs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/SearchMatrixPairs',
            points__pb2.SearchMatrixPoints.SerializeToString,
            points__pb2.SearchMatrixPairsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SearchMatrixOffsets(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/qdrant.Points/SearchMatrixOffsets',
            points__pb2.SearchMatrixPoints.SerializeToString,
            points__pb2.SearchMatrixOffsetsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
