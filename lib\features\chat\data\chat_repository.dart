import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:soul_reflector_app/core/env/env_config.dart';
import 'package:soul_reflector_app/models/message_model.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';

class ChatRepository {
  final String _apiKey = EnvConfig.apiKey;
  final String _baseUrl = EnvConfig.baseUrl;

  /// Builds full OpenAI message format from app messages
  List<Map<String, String>> _buildMessageHistory({
    required List<MessageModel> messages,
    required String systemPrompt,
    required String userInput,
  }) {
    final List<Map<String, String>> result = [];

    // Add system instruction
    result.add({"role": "system", "content": systemPrompt});

    // Include past messages
    for (final msg in messages) {
      result.add({"role": msg.role, "content": msg.content});
    }

    // Current user input (helps reinforce latest question)
    result.add({"role": "user", "content": userInput});

    return result;
  }

  /// Sends request to OpenAI and returns raw assistant reply (String)
  Future<String> fetchChatCompletion({
    required List<MessageModel> fullHistory,
    required String userInput,
    required String systemPrompt,
    String modelName = "gpt-4o",
  }) async {
    final url = Uri.parse("$_baseUrl/v1/chat/completions");

    final messages = _buildMessageHistory(
      messages: fullHistory,
      systemPrompt: systemPrompt,
      userInput: userInput,
    );

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
      },
      body: jsonEncode({
        "model": modelName,
        "messages": messages,
        "temperature": 0.9,
        "top_p": 1.0,
        "presence_penalty": 0.5,
        "frequency_penalty": 0.2,
      }),
    );

    if (response.statusCode == 200) {
      final decoded = jsonDecode(utf8.decode(response.bodyBytes));
      final reply = decoded['choices'][0]['message']['content'];
      return reply.trim();
    } else {
      throw Exception(
        '❌ GPT API error: ${response.statusCode} - ${response.body}',
      );
    }
  }

  /// Same as above, but wraps GPT reply in a MessageModel for UI
  Future<MessageModel> getChatResponse({
    required List<MessageModel> conversation,
    required String systemPrompt,
    required String userInput,
    PromptStyle? toneStyle,
  }) async {
    try {
      final rawReply = await fetchChatCompletion(
        fullHistory: conversation,
        userInput: userInput,
        systemPrompt: systemPrompt,
      );

      return MessageModel(
        role: 'assistant',
        content: rawReply,
        text: rawReply,
        isUser: false,
        toneStyle: toneStyle,
      );
    } catch (e) {
      const fallbackText =
          '⚠️ I wasn’t able to reflect deeply right now. Try again in a moment.';

      return MessageModel(
        role: 'assistant',
        content: fallbackText,
        text: fallbackText,
        isUser: false,
        toneStyle: toneStyle,
      );
    }
  }
}
