import 'package:soul_reflector_app/features/emotion/emotion_detector.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';

class SuggestionGenerator {
  /// Dynamic suggestion generation based on user input & detected tone
  static List<String> generateFromContext({
    required String userInput,
    required String aiResponse,
  }) {
    final tone =
        EmotionDetector.detectSuggestedTone(userInput) ??
        PromptStyle.knowYourself;

    final suggestionsByTone = {
      PromptStyle.innerDoctor: [
        "How do I recharge when I'm deeply tired?",
        "What is my body trying to tell me?",
        "How can nature help me feel whole again?",
      ],
      PromptStyle.truthBomb: [
        "What truth am I avoiding?",
        "Why do I lie to myself?",
        "What illusion needs to break in me?",
      ],
      PromptStyle.mindMirror: [
        "Why do I overthink everything?",
        "What’s underneath my mental clutter?",
        "How can I make peace with my inner noise?",
      ],
      PromptStyle.wisdomWhisper: [
        "What is grief trying to teach me?",
        "How do I listen to my quiet wisdom?",
        "Where can I find softness in my struggle?",
      ],
      PromptStyle.knowYourself: [
        "Who am I when no one is looking?",
        "How do I rediscover myself?",
        "What part of me is asking to be seen?",
      ],
      PromptStyle.innerFlame: [
        "What ignites my inner fire?",
        "How do I express my truth with passion?",
        "What blocks my creative spark?",
      ],
      PromptStyle.mysticLaughter: [
        "Why is everything so absurd?",
        "Can laughter be my teacher?",
        "What if the joke is on my ego?",
      ],
    };

    return suggestionsByTone[tone] ??
        [
          "What am I not seeing clearly?",
          "Why do I feel disconnected?",
          "Who is the real me?",
        ];
  }
}
