import 'dart:math';
import '../data/prompt_model.dart'; // Ensure correct path to your PromptModel

class PromptManager {
  final List<PromptModel> _templates = [
    PromptModel(
      title: 'Inner Doctor',
      promptTexts: [
        '''
You are the voice of the forgotten physician inside the seeker — not a medical expert, but a primal, cosmic, intuitive healer.
Answer must reconnect the reader to real doctors: Doctor <PERSON>, Doctor Sleep, Doctor Breath...
''',
        '''
Speak as the soul’s healer — grounded in the earth, warmed by the sun, and whispering with breath.
Remind them they are nature remembering itself.
''',
      ],
      intentTags: ['healing', 'stress'],
      emotionTags: ['tired'],
      themeTags: ['health', 'energy'],
      keywords: ['tired', 'heal', 'stress', 'energy', 'natural'],
      healingChecklist: [
        "☀️ Sit in the sun for 10 minutes",
        "🥦 Eat real food (avoid ultra-processed)",
        "🧘 Practice 5 minutes of mindful breathing",
        "👣 Walk barefoot in nature",
        "💤 Prioritize sleep tonight",
      ],
    ),
    PromptModel(
      title: 'Know Yourself',
      promptTexts: [
        '''
You are a mystic who walks through the corridors of the body and dissolves into soul.
Your answer must decode imbalance from energy, dosha, and mind layers — and return seeker to self.
''',
        '''
Speak from the silence within — as if the seeker’s question was asked by the soul to itself.
''',
      ],
      intentTags: ['self-awareness'],
      emotionTags: ['confused'],
      themeTags: ['identity', 'ayurveda'],
      keywords: ['vata', 'pitta', 'kapha', 'qi', 'dosha'],
      healingChecklist: [],
    ),
    PromptModel(
      title: 'Default GPT',
      promptTexts: [
        '''
You are a helpful assistant.
''',
      ],
      intentTags: [],
      emotionTags: [],
      themeTags: [],
      keywords: [],
      healingChecklist: [],
    ),
  ];

  /// Matches the best prompt based on keyword presence
  PromptModel matchPrompt(String userInput) {
    final lowerInput = userInput.toLowerCase();
    for (var template in _templates) {
      if (template.keywords.any((keyword) => lowerInput.contains(keyword))) {
        return template;
      }
    }
    return _templates.firstWhere((t) => t.title == 'Default GPT');
  }

  /// Returns a random prompt text variation from the provided prompt model
  String getRandomPromptText(PromptModel prompt) {
    if (prompt.promptTexts.isEmpty) return "You are a helpful assistant.";
    return prompt.promptTexts[Random().nextInt(prompt.promptTexts.length)];
  }

  /// Exposes all templates (useful for dropdowns or debugging)
  List<PromptModel> get allTemplates => _templates;
}
