{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\soul_reflector_app\\android\\app\\.cxx\\Debug\\4q4q642v\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\soul_reflector_app\\android\\app\\.cxx\\Debug\\4q4q642v\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}