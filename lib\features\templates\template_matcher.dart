import 'dart:convert';
import 'dart:developer';
import 'package:flutter/services.dart';

class TemplateMatcher {
  static Future<String?> matchTemplate(String userInput) async {
    final lowerInput = userInput.toLowerCase();

    try {
      final jsonString = await rootBundle.loadString(
        'assets/templates/prompt_templates.json',
      );
      final List<dynamic> templates = json.decode(jsonString);

      for (final template in templates) {
        final keywords = List<String>.from(template['keywords']);
        final matched = keywords.any(
          (keyword) => lowerInput.contains(keyword.toLowerCase()),
        );
        if (matched) return template['response'];
      }
    } catch (e) {
      log("❌ Template matching failed: $e", name: 'TemplateMatcher');
    }

    return null;
  }
}
