import 'dart:developer'; // ✅ Added for proper logging

import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_folder_loader.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_model.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_scoring_engine.dart';

class PromptMatcher {
  /// Entry point: returns the best matched prompt for a question and selected button style
  static Future<PromptModel?> getBestPrompt({
    required String userInput,
    required PromptStyle style,
  }) async {
    final allPrompts = await PromptFolderLoader.loadPrompts(style);

    if (allPrompts.isEmpty) {
      log(
        '⚠️ No prompts found for style: $style',
      ); // ✅ Replaced `print` with `log`
      return null;
    }

    return PromptScoringEngine.getBestPromptFromList(
      userInput: userInput,
      prompts: allPrompts,
    );
  }
}
