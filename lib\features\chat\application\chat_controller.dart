import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:soul_reflector_app/core/services/tts_service.dart';
import 'package:soul_reflector_app/features/chat/data/chat_repository.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_matcher.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';
import 'package:soul_reflector_app/features/emotion/emotion_detector.dart';
import 'package:soul_reflector_app/features/suggestions/data/suggestion_generator.dart';
import 'package:soul_reflector_app/models/message_model.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_model.dart';
import 'package:soul_reflector_app/features/templates/template_matcher.dart';
import 'package:soul_reflector_app/features/prompts/manager/prompt_manager.dart'; // ✅ Corrected path

class ChatController extends ChangeNotifier {
  static const String _tonePrefKey = 'selected_tone';
  static const String defaultSystemPrompt = '''
You are a mystical, poetic, meditative voice. Answer like a gentle spiritual teacher from no tradition. Invite awareness, not belief.
''';
  static const int _maxMessages = 100;

  final ChatRepository _repository;
  final List<MessageModel> _messages = [];
  final List<String> _suggestions = [];

  PromptStyle selectedStyle = PromptStyle.truthBomb;
  bool _isLoading = false;
  PromptModel? _matchedPrompt;

  ChatController(this._repository) {
    _loadSavedTone();
  }

  List<MessageModel> get messages => List.unmodifiable(_messages);
  List<String> get suggestions => List.unmodifiable(_suggestions);
  bool get isLoading => _isLoading;
  PromptModel? get matchedPrompt => _matchedPrompt;

  Future<void> _loadSavedTone() async {
    final prefs = await SharedPreferences.getInstance();
    final toneString = prefs.getString(_tonePrefKey);
    if (toneString != null) {
      selectedStyle = PromptStyle.values.firstWhere(
        (e) => e.name == toneString,
        orElse: () => PromptStyle.innerDoctor,
      );
      notifyListeners();
    }
  }

  Future<void> _saveTone(PromptStyle style) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tonePrefKey, style.name);
  }

  void selectStyle(PromptStyle style) {
    if (selectedStyle == style) return;
    selectedStyle = style;
    _saveTone(style);
    notifyListeners();
  }

  Future<void> sendMessage(String userInput) async {
    if (userInput.trim().isEmpty) return;

    try {
      _setLoading(true);
      _suggestions.clear();
      notifyListeners();

      _addUserMessage(userInput);

      // ✅ Step 1: Template match
      final matchedTemplate = await TemplateMatcher.matchTemplate(userInput);
      if (matchedTemplate != null) {
        _addAssistantMessage(matchedTemplate);
        _generateSuggestions(userInput, matchedTemplate);
        await TtsService.speak(matchedTemplate);
        _setLoading(false);
        return;
      }

      // ✅ Step 2: Emotion detection or selected tone
      final tone =
          selectedStyle.name.isNotEmpty
              ? selectedStyle
              : (EmotionDetector.detectSuggestedTone(userInput) ??
                  PromptStyle.innerDoctor);

      // ✅ Step 3: Get matching prompt + randomized promptText
      final prompt = await PromptMatcher.getBestPrompt(
        userInput: userInput,
        style: tone,
      );

      _matchedPrompt = prompt;

      final promptText =
          (prompt != null && prompt.promptTexts.isNotEmpty)
              ? PromptManager().getRandomPromptText(prompt)
              : defaultSystemPrompt;

      // ✅ Replace fullHistory with recent messages only
      final recentMessages =
          _messages.length > 8
              ? _messages.sublist(_messages.length - 8)
              : _messages;

      final aiResponse = await _repository.fetchChatCompletion(
        fullHistory: recentMessages,
        userInput: userInput,
        systemPrompt: promptText,
      );

      if (aiResponse.isEmpty || aiResponse.length < 20) {
        _addAssistantMessage(
          "I wasn’t able to reflect deeply right now. Let’s try again.",
        );
      } else {
        _addAssistantMessage(aiResponse);
        _generateSuggestions(userInput, aiResponse);
        await TtsService.speak(aiResponse);
      }
    } catch (e) {
      debugPrint('❌ Error in sendMessage: $e');
      _addAssistantMessage("Something went wrong. Let's try again.");
    } finally {
      _setLoading(false);
    }
  }

  void _addUserMessage(String text) {
    final userMessage = MessageModel(
      role: 'user',
      content: text,
      isUser: true,
      text: text,
      toneStyle: selectedStyle,
    );
    _messages.add(userMessage);
    notifyListeners();
  }

  void _addAssistantMessage(String text) {
    if (_messages.length >= _maxMessages) {
      _messages.removeAt(0);
    }

    final assistantMessage = MessageModel(
      role: 'assistant',
      content: text,
      isUser: false,
      text: text,
      toneStyle: selectedStyle,
    );
    _messages.add(assistantMessage);
    notifyListeners();
  }

  void _generateSuggestions(String userInput, String aiResponse) {
    final newSuggestions = SuggestionGenerator.generateFromContext(
      userInput: userInput,
      aiResponse: aiResponse,
    );
    _suggestions.addAll(newSuggestions);
    notifyListeners();
  }

  void resetChat() {
    _messages.clear();
    _suggestions.clear();
    _matchedPrompt = null;
    notifyListeners();
  }

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }
}
