class SuggestionModel {
  final String id;
  final String text;

  SuggestionModel({required this.id, required this.text});

  // Optional: toJson for saving
  Map<String, dynamic> toJson() => {'id': id, 'text': text};

  // Optional: fromJson for loading
  factory SuggestionModel.fromJson(Map<String, dynamic> json) {
    return SuggestionModel(id: json['id'], text: json['text']);
  }

  // Optional: override == and hashCode for comparisons
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SuggestionModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          text == other.text;

  @override
  int get hashCode => id.hashCode ^ text.hashCode;

  @override
  String toString() => 'SuggestionModel(id: $id, text: $text)';
}
