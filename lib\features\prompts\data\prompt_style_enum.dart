enum PromptStyle {
  truthBomb,
  inner<PERSON>lame,
  mind<PERSON><PERSON><PERSON><PERSON>,
  wisdom<PERSON><PERSON>per,
  mystic<PERSON><PERSON><PERSON>,
  innerDoctor,
  knowYourself,
}

extension PromptStyleExtension on PromptStyle {
  String get label {
    switch (this) {
      case PromptStyle.truthBomb:
        return "Truth Bomb";
      case PromptStyle.innerFlame:
        return "Inner Flame";
      case PromptStyle.mindMirror:
        return "Mind Mirror";
      case PromptStyle.wisdomWhisper:
        return "Wisdom Whisper";
      case PromptStyle.mysticLaughter:
        return "Mystic Laughter";
      case PromptStyle.innerDoctor:
        return "Inner Doctor";
      case PromptStyle.knowYourself:
        return "Know Yourself";
    }
  }

  String get emoji {
    switch (this) {
      case PromptStyle.truthBomb:
        return "💣";
      case PromptStyle.innerFlame:
        return "🔥";
      case PromptStyle.mindMirror:
        return "🧠";
      case PromptStyle.wisdomWhisper:
        return "🌬️";
      case PromptStyle.mysticLaughter:
        return "😂";
      case PromptStyle.innerDoctor:
        return "🌿";
      case PromptStyle.knowYourself:
        return "🔍";
    }
  }

  String get folderFileName {
    switch (this) {
      case PromptStyle.truthBomb:
        return 'truth_bomb_prompts.json';
      case PromptStyle.innerFlame:
        return 'inner_flame_prompts.json';
      case PromptStyle.mindMirror:
        return 'mind_mirror_prompts.json';
      case PromptStyle.wisdomWhisper:
        return 'wisdom_whisper_prompts.json';
      case PromptStyle.mysticLaughter:
        return 'mystic_laughter_prompts.json';
      case PromptStyle.innerDoctor:
        return 'inner_doctor_prompts.json';
      case PromptStyle.knowYourself:
        return 'know_yourself_prompts.json';
    }
  }
}
