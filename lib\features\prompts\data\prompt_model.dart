// 📁 lib/features/prompts/data/prompt_model.dart

class PromptModel {
  /// A unique name for identifying this prompt internally
  final String title;

  /// The actual prompt variations sent to GPT as a system message (rotated)
  final List<String> promptTexts;

  /// Tags representing the user's goal or intent (e.g. clarity, healing)
  final List<String> intentTags;

  /// Emotional undertones in the user's input (e.g. anxious, stuck)
  final List<String> emotionTags;

  /// Conceptual or philosophical themes (e.g. ego, identity, truth)
  final List<String> themeTags;

  /// Plain keywords for fallback matching when tags don’t match
  final List<String> keywords;

  /// ✅ Optional list of healing checklist steps (used for Inner Doctor-style prompts)
  final List<String> healingChecklist;

  PromptModel({
    required this.title,
    required this.promptTexts,
    required this.intentTags,
    required this.emotionTags,
    required this.themeTags,
    required this.keywords,
    this.healingChecklist = const [], // ✅ default ensures safe fallback
  });

  /// Factory method to create a PromptModel from JSON
  factory PromptModel.fromJson(Map<String, dynamic> json) {
    return PromptModel(
      title: json['title'] ?? '',
      promptTexts: List<String>.from(json['promptTexts'] ?? []),
      intentTags: List<String>.from(json['intentTags'] ?? []),
      emotionTags: List<String>.from(json['emotionTags'] ?? []),
      themeTags: List<String>.from(json['themeTags'] ?? []),
      keywords: List<String>.from(json['keywords'] ?? []),
      healingChecklist: List<String>.from(
        json['healingChecklist'] ?? [],
      ), // ✅ safe fallback
    );
  }

  /// Convert PromptModel instance to JSON (for saving/exporting if needed)
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'promptTexts': promptTexts,
      'intentTags': intentTags,
      'emotionTags': emotionTags,
      'themeTags': themeTags,
      'keywords': keywords,
      'healingChecklist': healingChecklist,
    };
  }

  /// Optional: Friendly toString for debugging
  @override
  String toString() {
    return 'PromptModel(title: $title)';
  }
}
