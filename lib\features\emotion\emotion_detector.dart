import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';

class EmotionDetector {
  static final Map<PromptStyle, List<String>> emotionKeywords = {
    PromptStyle.innerDoctor: [
      'overwhelmed',
      'tired',
      'lost',
      'disconnected',
      'sad',
      'exhausted',
      'can’t let go',
      'fatigue',
      'drained',
    ],
    PromptStyle.mindMirror: [
      'confused',
      'overthinking',
      'anxious',
      'complicated',
      'inner conflict',
    ],
    PromptStyle.truthBomb: [
      'lying',
      'fake',
      'illusion',
      'ego',
      'delusion',
      'society',
    ],
    PromptStyle.wisdomWhisper: [
      'grief',
      'grieving',
      'gentle',
      'painful',
      'fragile',
    ],
    PromptStyle.knowYourself: [
      'self-doubt',
      'identity',
      'who am I',
      'not myself',
    ],
    PromptStyle.mysticLaughter: [
      'bored',
      'nothing makes sense',
      'what’s the point',
    ],
  };

  /// Returns best-matched tone based on emotion keywords.
  static PromptStyle? detectSuggestedTone(String userInput) {
    final lowerInput = userInput.toLowerCase();

    for (final entry in emotionKeywords.entries) {
      for (final keyword in entry.value) {
        if (lowerInput.contains(keyword.toLowerCase())) {
          return entry.key;
        }
      }
    }

    return null;
  }
}
