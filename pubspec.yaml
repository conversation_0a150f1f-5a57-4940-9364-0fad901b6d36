name: soul_reflector_app
description: "A GPT-powered spiritual reflection app."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # Core UI
  cupertino_icons: ^1.0.8
  provider: ^6.1.0

  # Networking & Environment
  http: ^0.13.6
  flutter_dotenv: ^5.0.2

  # Text-to-speech
  flutter_tts: ^3.8.2

  # Optional: Animated text effects (for mystic style UI, later)
  animated_text_kit: ^4.2.2

  # Shared preferences for saving user settings or favorites
  shared_preferences: ^2.2.2

  # ✅ Markdown rendering for GPT responses
  flutter_markdown: ^0.6.17

  # ✅ Logging support
  logging: ^1.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

flutter:

  uses-material-design: true

  assets:
    - assets/images/
    - assets/audio/
    - assets/fonts/
    - assets/prompts/
    - assets/templates/
    - .env

  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/NotoSans-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: assets/fonts/NotoSans-Italic.ttf
          style: italic
        - asset: assets/fonts/NotoSans-Italic-VariableFont_wdth,wght.ttf
        - asset: assets/fonts/NotoSans-VariableFont_wdth,wght.ttf
    - family: NotoColorEmoji
      fonts:
        - asset: assets/fonts/NotoColorEmoji-Regular.ttf
    - family: NotoSansDevanagari
      fonts:
        - asset: assets/fonts/NotoSansDevanagari-Regular.ttf
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
