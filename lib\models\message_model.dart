import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';

class MessageModel {
  final String role; // 'user', 'assistant', or 'system'
  final String content;
  final DateTime timestamp;

  /// Newly added for UI rendering convenience
  final String text;
  final bool isUser;
  final PromptStyle? toneStyle; // Optional for assistant messages only

  MessageModel({
    required this.role,
    required this.content,
    DateTime? timestamp,
    this.text = '',
    this.isUser = false,
    this.toneStyle,
  }) : timestamp = timestamp ?? DateTime.now();

  /// GPT format: {"role": "user", "content": "Hello"}
  Map<String, String> toMap() {
    return {'role': role, 'content': content};
  }

  /// Full object for local storage (if needed later)
  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'text': text,
      'isUser': isUser,
      'toneStyle': toneStyle?.name,
    };
  }

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      role: json['role'] ?? 'user',
      content: json['content'] ?? '',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      text: json['text'] ?? '',
      isUser: json['isUser'] ?? false,
      toneStyle:
          json['toneStyle'] != null
              ? PromptStyle.values.byName(json['toneStyle'])
              : null,
    );
  }

  /// Clone with updated fields
  MessageModel copyWith({
    String? role,
    String? content,
    DateTime? timestamp,
    String? text,
    bool? isUser,
    PromptStyle? toneStyle,
  }) {
    return MessageModel(
      role: role ?? this.role,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      text: text ?? this.text,
      isUser: isUser ?? this.isUser,
      toneStyle: toneStyle ?? this.toneStyle,
    );
  }
}
