# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: points.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import collections_pb2 as collections__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from . import json_with_int_pb2 as json__with__int__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0cpoints.proto\x12\x06qdrant\x1a\x11\x63ollections.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x13json_with_int.proto\"8\n\rWriteOrdering\x12\'\n\x04type\x18\x01 \x01(\x0e\x32\x19.qdrant.WriteOrderingType\"Y\n\x0fReadConsistency\x12+\n\x04type\x18\x01 \x01(\x0e\x32\x1b.qdrant.ReadConsistencyTypeH\x00\x12\x10\n\x06\x66\x61\x63tor\x18\x02 \x01(\x04H\x00\x42\x07\n\x05value\"<\n\x07PointId\x12\r\n\x03num\x18\x01 \x01(\x04H\x00\x12\x0e\n\x04uuid\x18\x02 \x01(\tH\x00\x42\x12\n\x10point_id_options\"\x1d\n\rSparseIndices\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\r\"\x96\x01\n\x08\x44ocument\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\r\n\x05model\x18\x03 \x01(\t\x12.\n\x07options\x18\x04 \x03(\x0b\x32\x1d.qdrant.Document.OptionsEntry\x1a=\n\x0cOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\"\xa0\x01\n\x05Image\x12\x1c\n\x05image\x18\x01 \x01(\x0b\x32\r.qdrant.Value\x12\r\n\x05model\x18\x02 \x01(\t\x12+\n\x07options\x18\x03 \x03(\x0b\x32\x1a.qdrant.Image.OptionsEntry\x1a=\n\x0cOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\"\xb5\x01\n\x0fInferenceObject\x12\x1d\n\x06object\x18\x01 \x01(\x0b\x32\r.qdrant.Value\x12\r\n\x05model\x18\x02 \x01(\t\x12\x35\n\x07options\x18\x03 \x03(\x0b\x32$.qdrant.InferenceObject.OptionsEntry\x1a=\n\x0cOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\"\xf7\x02\n\x06Vector\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x02\x12+\n\x07indices\x18\x02 \x01(\x0b\x32\x15.qdrant.SparseIndicesH\x01\x88\x01\x01\x12\x1a\n\rvectors_count\x18\x03 \x01(\rH\x02\x88\x01\x01\x12$\n\x05\x64\x65nse\x18\x65 \x01(\x0b\x32\x13.qdrant.DenseVectorH\x00\x12&\n\x06sparse\x18\x66 \x01(\x0b\x32\x14.qdrant.SparseVectorH\x00\x12/\n\x0bmulti_dense\x18g \x01(\x0b\x32\x18.qdrant.MultiDenseVectorH\x00\x12$\n\x08\x64ocument\x18h \x01(\x0b\x32\x10.qdrant.DocumentH\x00\x12\x1e\n\x05image\x18i \x01(\x0b\x32\r.qdrant.ImageH\x00\x12)\n\x06object\x18j \x01(\x0b\x32\x17.qdrant.InferenceObjectH\x00\x42\x08\n\x06vectorB\n\n\x08_indicesB\x10\n\x0e_vectors_count\"\x8c\x02\n\x0cVectorOutput\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x02\x12+\n\x07indices\x18\x02 \x01(\x0b\x32\x15.qdrant.SparseIndicesH\x01\x88\x01\x01\x12\x1a\n\rvectors_count\x18\x03 \x01(\rH\x02\x88\x01\x01\x12$\n\x05\x64\x65nse\x18\x65 \x01(\x0b\x32\x13.qdrant.DenseVectorH\x00\x12&\n\x06sparse\x18\x66 \x01(\x0b\x32\x14.qdrant.SparseVectorH\x00\x12/\n\x0bmulti_dense\x18g \x01(\x0b\x32\x18.qdrant.MultiDenseVectorH\x00\x42\x08\n\x06vectorB\n\n\x08_indicesB\x10\n\x0e_vectors_count\"\x1b\n\x0b\x44\x65nseVector\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x02\"/\n\x0cSparseVector\x12\x0e\n\x06values\x18\x01 \x03(\x02\x12\x0f\n\x07indices\x18\x02 \x03(\r\"8\n\x10MultiDenseVector\x12$\n\x07vectors\x18\x01 \x03(\x0b\x32\x13.qdrant.DenseVector\"\xa7\x02\n\x0bVectorInput\x12\x1d\n\x02id\x18\x01 \x01(\x0b\x32\x0f.qdrant.PointIdH\x00\x12$\n\x05\x64\x65nse\x18\x02 \x01(\x0b\x32\x13.qdrant.DenseVectorH\x00\x12&\n\x06sparse\x18\x03 \x01(\x0b\x32\x14.qdrant.SparseVectorH\x00\x12/\n\x0bmulti_dense\x18\x04 \x01(\x0b\x32\x18.qdrant.MultiDenseVectorH\x00\x12$\n\x08\x64ocument\x18\x05 \x01(\x0b\x32\x10.qdrant.DocumentH\x00\x12\x1e\n\x05image\x18\x06 \x01(\x0b\x32\r.qdrant.ImageH\x00\x12)\n\x06object\x18\x07 \x01(\x0b\x32\x17.qdrant.InferenceObjectH\x00\x42\t\n\x07variant\"8\n\x10ShardKeySelector\x12$\n\nshard_keys\x18\x01 \x03(\x0b\x32\x10.qdrant.ShardKey\"\xf5\x01\n\x0cUpsertPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12#\n\x06points\x18\x03 \x03(\x0b\x32\x13.qdrant.PointStruct\x12,\n\x08ordering\x18\x04 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x01\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x05 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x02\x88\x01\x01\x42\x07\n\x05_waitB\x0b\n\t_orderingB\x15\n\x13_shard_key_selector\"\xf8\x01\n\x0c\x44\x65letePoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12&\n\x06points\x18\x03 \x01(\x0b\x32\x16.qdrant.PointsSelector\x12,\n\x08ordering\x18\x04 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x01\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x05 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x02\x88\x01\x01\x42\x07\n\x05_waitB\x0b\n\t_orderingB\x15\n\x13_shard_key_selector\"\x85\x03\n\tGetPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x1c\n\x03ids\x18\x02 \x03(\x0b\x32\x0f.qdrant.PointId\x12\x31\n\x0cwith_payload\x18\x04 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelector\x12\x36\n\x0cwith_vectors\x18\x05 \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x00\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x06 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x01\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x07 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x02\x88\x01\x01\x12\x14\n\x07timeout\x18\x08 \x01(\x04H\x03\x88\x01\x01\x42\x0f\n\r_with_vectorsB\x13\n\x11_read_consistencyB\x15\n\x13_shard_key_selectorB\n\n\x08_timeoutJ\x04\x08\x03\x10\x04\"\xfc\x01\n\x12UpdatePointVectors\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12$\n\x06points\x18\x03 \x03(\x0b\x32\x14.qdrant.PointVectors\x12,\n\x08ordering\x18\x04 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x01\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x05 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x02\x88\x01\x01\x42\x07\n\x05_waitB\x0b\n\t_orderingB\x15\n\x13_shard_key_selector\"M\n\x0cPointVectors\x12\x1b\n\x02id\x18\x01 \x01(\x0b\x32\x0f.qdrant.PointId\x12 \n\x07vectors\x18\x02 \x01(\x0b\x32\x0f.qdrant.Vectors\"\xb1\x02\n\x12\x44\x65letePointVectors\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12/\n\x0fpoints_selector\x18\x03 \x01(\x0b\x32\x16.qdrant.PointsSelector\x12(\n\x07vectors\x18\x04 \x01(\x0b\x32\x17.qdrant.VectorsSelector\x12,\n\x08ordering\x18\x05 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x01\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x06 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x02\x88\x01\x01\x42\x07\n\x05_waitB\x0b\n\t_orderingB\x15\n\x13_shard_key_selector\"\xb5\x03\n\x10SetPayloadPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x36\n\x07payload\x18\x03 \x03(\x0b\x32%.qdrant.SetPayloadPoints.PayloadEntry\x12\x34\n\x0fpoints_selector\x18\x05 \x01(\x0b\x32\x16.qdrant.PointsSelectorH\x01\x88\x01\x01\x12,\n\x08ordering\x18\x06 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x02\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x07 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x03\x88\x01\x01\x12\x10\n\x03key\x18\x08 \x01(\tH\x04\x88\x01\x01\x1a=\n\x0cPayloadEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\x42\x07\n\x05_waitB\x12\n\x10_points_selectorB\x0b\n\t_orderingB\x15\n\x13_shard_key_selectorB\x06\n\x04_keyJ\x04\x08\x04\x10\x05\"\xb5\x02\n\x13\x44\x65letePayloadPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x0c\n\x04keys\x18\x03 \x03(\t\x12\x34\n\x0fpoints_selector\x18\x05 \x01(\x0b\x32\x16.qdrant.PointsSelectorH\x01\x88\x01\x01\x12,\n\x08ordering\x18\x06 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x02\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x07 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x03\x88\x01\x01\x42\x07\n\x05_waitB\x12\n\x10_points_selectorB\x0b\n\t_orderingB\x15\n\x13_shard_key_selectorJ\x04\x08\x04\x10\x05\"\xfe\x01\n\x12\x43learPayloadPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12&\n\x06points\x18\x03 \x01(\x0b\x32\x16.qdrant.PointsSelector\x12,\n\x08ordering\x18\x04 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x01\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x05 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x02\x88\x01\x01\x42\x07\n\x05_waitB\x0b\n\t_orderingB\x15\n\x13_shard_key_selector\"\xaf\x02\n\x1a\x43reateFieldIndexCollection\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x12\n\nfield_name\x18\x03 \x01(\t\x12*\n\nfield_type\x18\x04 \x01(\x0e\x32\x11.qdrant.FieldTypeH\x01\x88\x01\x01\x12;\n\x12\x66ield_index_params\x18\x05 \x01(\x0b\x32\x1a.qdrant.PayloadIndexParamsH\x02\x88\x01\x01\x12,\n\x08ordering\x18\x06 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x03\x88\x01\x01\x42\x07\n\x05_waitB\r\n\x0b_field_typeB\x15\n\x13_field_index_paramsB\x0b\n\t_ordering\"\xa0\x01\n\x1a\x44\x65leteFieldIndexCollection\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x12\n\nfield_name\x18\x03 \x01(\t\x12,\n\x08ordering\x18\x04 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x01\x88\x01\x01\x42\x07\n\x05_waitB\x0b\n\t_ordering\"(\n\x16PayloadIncludeSelector\x12\x0e\n\x06\x66ields\x18\x01 \x03(\t\"(\n\x16PayloadExcludeSelector\x12\x0e\n\x06\x66ields\x18\x01 \x03(\t\"\xa1\x01\n\x13WithPayloadSelector\x12\x10\n\x06\x65nable\x18\x01 \x01(\x08H\x00\x12\x31\n\x07include\x18\x02 \x01(\x0b\x32\x1e.qdrant.PayloadIncludeSelectorH\x00\x12\x31\n\x07\x65xclude\x18\x03 \x01(\x0b\x32\x1e.qdrant.PayloadExcludeSelectorH\x00\x42\x12\n\x10selector_options\"\x82\x01\n\x0cNamedVectors\x12\x32\n\x07vectors\x18\x01 \x03(\x0b\x32!.qdrant.NamedVectors.VectorsEntry\x1a>\n\x0cVectorsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1d\n\x05value\x18\x02 \x01(\x0b\x32\x0e.qdrant.Vector:\x02\x38\x01\"\x94\x01\n\x12NamedVectorsOutput\x12\x38\n\x07vectors\x18\x01 \x03(\x0b\x32\'.qdrant.NamedVectorsOutput.VectorsEntry\x1a\x44\n\x0cVectorsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.qdrant.VectorOutput:\x02\x38\x01\"g\n\x07Vectors\x12 \n\x06vector\x18\x01 \x01(\x0b\x32\x0e.qdrant.VectorH\x00\x12\'\n\x07vectors\x18\x02 \x01(\x0b\x32\x14.qdrant.NamedVectorsH\x00\x42\x11\n\x0fvectors_options\"y\n\rVectorsOutput\x12&\n\x06vector\x18\x01 \x01(\x0b\x32\x14.qdrant.VectorOutputH\x00\x12-\n\x07vectors\x18\x02 \x01(\x0b\x32\x1a.qdrant.NamedVectorsOutputH\x00\x42\x11\n\x0fvectors_options\" \n\x0fVectorsSelector\x12\r\n\x05names\x18\x01 \x03(\t\"g\n\x13WithVectorsSelector\x12\x10\n\x06\x65nable\x18\x01 \x01(\x08H\x00\x12*\n\x07include\x18\x02 \x01(\x0b\x32\x17.qdrant.VectorsSelectorH\x00\x42\x12\n\x10selector_options\"\x88\x01\n\x18QuantizationSearchParams\x12\x13\n\x06ignore\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x14\n\x07rescore\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12\x19\n\x0coversampling\x18\x03 \x01(\x01H\x02\x88\x01\x01\x42\t\n\x07_ignoreB\n\n\x08_rescoreB\x0f\n\r_oversampling\"\xc8\x01\n\x0cSearchParams\x12\x14\n\x07hnsw_ef\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x12\n\x05\x65xact\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12;\n\x0cquantization\x18\x03 \x01(\x0b\x32 .qdrant.QuantizationSearchParamsH\x02\x88\x01\x01\x12\x19\n\x0cindexed_only\x18\x04 \x01(\x08H\x03\x88\x01\x01\x42\n\n\x08_hnsw_efB\x08\n\x06_exactB\x0f\n\r_quantizationB\x0f\n\r_indexed_only\"\x92\x05\n\x0cSearchPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x0e\n\x06vector\x18\x02 \x03(\x02\x12\x1e\n\x06\x66ilter\x18\x03 \x01(\x0b\x32\x0e.qdrant.Filter\x12\r\n\x05limit\x18\x04 \x01(\x04\x12\x31\n\x0cwith_payload\x18\x06 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelector\x12$\n\x06params\x18\x07 \x01(\x0b\x32\x14.qdrant.SearchParams\x12\x1c\n\x0fscore_threshold\x18\x08 \x01(\x02H\x00\x88\x01\x01\x12\x13\n\x06offset\x18\t \x01(\x04H\x01\x88\x01\x01\x12\x18\n\x0bvector_name\x18\n \x01(\tH\x02\x88\x01\x01\x12\x36\n\x0cwith_vectors\x18\x0b \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x03\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x0c \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x04\x88\x01\x01\x12\x14\n\x07timeout\x18\r \x01(\x04H\x05\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x0e \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x06\x88\x01\x01\x12\x32\n\x0esparse_indices\x18\x0f \x01(\x0b\x32\x15.qdrant.SparseIndicesH\x07\x88\x01\x01\x42\x12\n\x10_score_thresholdB\t\n\x07_offsetB\x0e\n\x0c_vector_nameB\x0f\n\r_with_vectorsB\x13\n\x11_read_consistencyB\n\n\x08_timeoutB\x15\n\x13_shard_key_selectorB\x11\n\x0f_sparse_indicesJ\x04\x08\x05\x10\x06\"\xc8\x01\n\x11SearchBatchPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12+\n\rsearch_points\x18\x02 \x03(\x0b\x32\x14.qdrant.SearchPoints\x12\x36\n\x10read_consistency\x18\x03 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x00\x88\x01\x01\x12\x14\n\x07timeout\x18\x04 \x01(\x04H\x01\x88\x01\x01\x42\x13\n\x11_read_consistencyB\n\n\x08_timeout\"\xb2\x01\n\nWithLookup\x12\x12\n\ncollection\x18\x01 \x01(\t\x12\x36\n\x0cwith_payload\x18\x02 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelectorH\x00\x88\x01\x01\x12\x36\n\x0cwith_vectors\x18\x03 \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x01\x88\x01\x01\x42\x0f\n\r_with_payloadB\x0f\n\r_with_vectors\"\xd5\x05\n\x11SearchPointGroups\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x0e\n\x06vector\x18\x02 \x03(\x02\x12\x1e\n\x06\x66ilter\x18\x03 \x01(\x0b\x32\x0e.qdrant.Filter\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x31\n\x0cwith_payload\x18\x05 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelector\x12$\n\x06params\x18\x06 \x01(\x0b\x32\x14.qdrant.SearchParams\x12\x1c\n\x0fscore_threshold\x18\x07 \x01(\x02H\x00\x88\x01\x01\x12\x18\n\x0bvector_name\x18\x08 \x01(\tH\x01\x88\x01\x01\x12\x36\n\x0cwith_vectors\x18\t \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x02\x88\x01\x01\x12\x10\n\x08group_by\x18\n \x01(\t\x12\x12\n\ngroup_size\x18\x0b \x01(\r\x12\x36\n\x10read_consistency\x18\x0c \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x03\x88\x01\x01\x12,\n\x0bwith_lookup\x18\r \x01(\x0b\x32\x12.qdrant.WithLookupH\x04\x88\x01\x01\x12\x14\n\x07timeout\x18\x0e \x01(\x04H\x05\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x0f \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x06\x88\x01\x01\x12\x32\n\x0esparse_indices\x18\x10 \x01(\x0b\x32\x15.qdrant.SparseIndicesH\x07\x88\x01\x01\x42\x12\n\x10_score_thresholdB\x0e\n\x0c_vector_nameB\x0f\n\r_with_vectorsB\x13\n\x11_read_consistencyB\x0e\n\x0c_with_lookupB\n\n\x08_timeoutB\x15\n\x13_shard_key_selectorB\x11\n\x0f_sparse_indices\"}\n\tStartFrom\x12\x0f\n\x05\x66loat\x18\x01 \x01(\x01H\x00\x12\x11\n\x07integer\x18\x02 \x01(\x03H\x00\x12/\n\ttimestamp\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x00\x12\x12\n\x08\x64\x61tetime\x18\x04 \x01(\tH\x00\x42\x07\n\x05value\"\x8a\x01\n\x07OrderBy\x12\x0b\n\x03key\x18\x01 \x01(\t\x12)\n\tdirection\x18\x02 \x01(\x0e\x32\x11.qdrant.DirectionH\x00\x88\x01\x01\x12*\n\nstart_from\x18\x03 \x01(\x0b\x32\x11.qdrant.StartFromH\x01\x88\x01\x01\x42\x0c\n\n_directionB\r\n\x0b_start_from\"\x8e\x04\n\x0cScrollPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x1e\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x0e.qdrant.Filter\x12$\n\x06offset\x18\x03 \x01(\x0b\x32\x0f.qdrant.PointIdH\x00\x88\x01\x01\x12\x12\n\x05limit\x18\x04 \x01(\rH\x01\x88\x01\x01\x12\x31\n\x0cwith_payload\x18\x06 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelector\x12\x36\n\x0cwith_vectors\x18\x07 \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x02\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x08 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x03\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\t \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x04\x88\x01\x01\x12&\n\x08order_by\x18\n \x01(\x0b\x32\x0f.qdrant.OrderByH\x05\x88\x01\x01\x12\x14\n\x07timeout\x18\x0b \x01(\x04H\x06\x88\x01\x01\x42\t\n\x07_offsetB\x08\n\x06_limitB\x0f\n\r_with_vectorsB\x13\n\x11_read_consistencyB\x15\n\x13_shard_key_selectorB\x0b\n\t_order_byB\n\n\x08_timeoutJ\x04\x08\x05\x10\x06\"\xa5\x01\n\x0eLookupLocation\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x18\n\x0bvector_name\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x03 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x01\x88\x01\x01\x42\x0e\n\x0c_vector_nameB\x15\n\x13_shard_key_selector\"\xcd\x06\n\x0fRecommendPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12!\n\x08positive\x18\x02 \x03(\x0b\x32\x0f.qdrant.PointId\x12!\n\x08negative\x18\x03 \x03(\x0b\x32\x0f.qdrant.PointId\x12\x1e\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x0e.qdrant.Filter\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x31\n\x0cwith_payload\x18\x07 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelector\x12$\n\x06params\x18\x08 \x01(\x0b\x32\x14.qdrant.SearchParams\x12\x1c\n\x0fscore_threshold\x18\t \x01(\x02H\x00\x88\x01\x01\x12\x13\n\x06offset\x18\n \x01(\x04H\x01\x88\x01\x01\x12\x12\n\x05using\x18\x0b \x01(\tH\x02\x88\x01\x01\x12\x36\n\x0cwith_vectors\x18\x0c \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x03\x88\x01\x01\x12\x30\n\x0blookup_from\x18\r \x01(\x0b\x32\x16.qdrant.LookupLocationH\x04\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x0e \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x05\x88\x01\x01\x12\x30\n\x08strategy\x18\x10 \x01(\x0e\x32\x19.qdrant.RecommendStrategyH\x06\x88\x01\x01\x12(\n\x10positive_vectors\x18\x11 \x03(\x0b\x32\x0e.qdrant.Vector\x12(\n\x10negative_vectors\x18\x12 \x03(\x0b\x32\x0e.qdrant.Vector\x12\x14\n\x07timeout\x18\x13 \x01(\x04H\x07\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x14 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x08\x88\x01\x01\x42\x12\n\x10_score_thresholdB\t\n\x07_offsetB\x08\n\x06_usingB\x0f\n\r_with_vectorsB\x0e\n\x0c_lookup_fromB\x13\n\x11_read_consistencyB\x0b\n\t_strategyB\n\n\x08_timeoutB\x15\n\x13_shard_key_selectorJ\x04\x08\x06\x10\x07\"\xd1\x01\n\x14RecommendBatchPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x31\n\x10recommend_points\x18\x02 \x03(\x0b\x32\x17.qdrant.RecommendPoints\x12\x36\n\x10read_consistency\x18\x03 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x00\x88\x01\x01\x12\x14\n\x07timeout\x18\x04 \x01(\x04H\x01\x88\x01\x01\x42\x13\n\x11_read_consistencyB\n\n\x08_timeout\"\x90\x07\n\x14RecommendPointGroups\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12!\n\x08positive\x18\x02 \x03(\x0b\x32\x0f.qdrant.PointId\x12!\n\x08negative\x18\x03 \x03(\x0b\x32\x0f.qdrant.PointId\x12\x1e\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x0e.qdrant.Filter\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x31\n\x0cwith_payload\x18\x06 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelector\x12$\n\x06params\x18\x07 \x01(\x0b\x32\x14.qdrant.SearchParams\x12\x1c\n\x0fscore_threshold\x18\x08 \x01(\x02H\x00\x88\x01\x01\x12\x12\n\x05using\x18\t \x01(\tH\x01\x88\x01\x01\x12\x36\n\x0cwith_vectors\x18\n \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x02\x88\x01\x01\x12\x30\n\x0blookup_from\x18\x0b \x01(\x0b\x32\x16.qdrant.LookupLocationH\x03\x88\x01\x01\x12\x10\n\x08group_by\x18\x0c \x01(\t\x12\x12\n\ngroup_size\x18\r \x01(\r\x12\x36\n\x10read_consistency\x18\x0e \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x04\x88\x01\x01\x12,\n\x0bwith_lookup\x18\x0f \x01(\x0b\x32\x12.qdrant.WithLookupH\x05\x88\x01\x01\x12\x30\n\x08strategy\x18\x11 \x01(\x0e\x32\x19.qdrant.RecommendStrategyH\x06\x88\x01\x01\x12(\n\x10positive_vectors\x18\x12 \x03(\x0b\x32\x0e.qdrant.Vector\x12(\n\x10negative_vectors\x18\x13 \x03(\x0b\x32\x0e.qdrant.Vector\x12\x14\n\x07timeout\x18\x14 \x01(\x04H\x07\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x15 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x08\x88\x01\x01\x42\x12\n\x10_score_thresholdB\x08\n\x06_usingB\x0f\n\r_with_vectorsB\x0e\n\x0c_lookup_fromB\x13\n\x11_read_consistencyB\x0e\n\x0c_with_lookupB\x0b\n\t_strategyB\n\n\x08_timeoutB\x15\n\x13_shard_key_selector\"A\n\x0cTargetVector\x12\'\n\x06single\x18\x01 \x01(\x0b\x32\x15.qdrant.VectorExampleH\x00\x42\x08\n\x06target\"[\n\rVectorExample\x12\x1d\n\x02id\x18\x01 \x01(\x0b\x32\x0f.qdrant.PointIdH\x00\x12 \n\x06vector\x18\x02 \x01(\x0b\x32\x0e.qdrant.VectorH\x00\x42\t\n\x07\x65xample\"f\n\x12\x43ontextExamplePair\x12\'\n\x08positive\x18\x01 \x01(\x0b\x32\x15.qdrant.VectorExample\x12\'\n\x08negative\x18\x02 \x01(\x0b\x32\x15.qdrant.VectorExample\"\x8e\x05\n\x0e\x44iscoverPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12$\n\x06target\x18\x02 \x01(\x0b\x32\x14.qdrant.TargetVector\x12+\n\x07\x63ontext\x18\x03 \x03(\x0b\x32\x1a.qdrant.ContextExamplePair\x12\x1e\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x0e.qdrant.Filter\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x31\n\x0cwith_payload\x18\x06 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelector\x12$\n\x06params\x18\x07 \x01(\x0b\x32\x14.qdrant.SearchParams\x12\x13\n\x06offset\x18\x08 \x01(\x04H\x00\x88\x01\x01\x12\x12\n\x05using\x18\t \x01(\tH\x01\x88\x01\x01\x12\x36\n\x0cwith_vectors\x18\n \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x02\x88\x01\x01\x12\x30\n\x0blookup_from\x18\x0b \x01(\x0b\x32\x16.qdrant.LookupLocationH\x03\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x0c \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x04\x88\x01\x01\x12\x14\n\x07timeout\x18\r \x01(\x04H\x05\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x0e \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x06\x88\x01\x01\x42\t\n\x07_offsetB\x08\n\x06_usingB\x0f\n\r_with_vectorsB\x0e\n\x0c_lookup_fromB\x13\n\x11_read_consistencyB\n\n\x08_timeoutB\x15\n\x13_shard_key_selector\"\xce\x01\n\x13\x44iscoverBatchPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12/\n\x0f\x64iscover_points\x18\x02 \x03(\x0b\x32\x16.qdrant.DiscoverPoints\x12\x36\n\x10read_consistency\x18\x03 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x00\x88\x01\x01\x12\x14\n\x07timeout\x18\x04 \x01(\x04H\x01\x88\x01\x01\x42\x13\n\x11_read_consistencyB\n\n\x08_timeout\"\xa5\x02\n\x0b\x43ountPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x1e\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x0e.qdrant.Filter\x12\x12\n\x05\x65xact\x18\x03 \x01(\x08H\x00\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x04 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x01\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x05 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x02\x88\x01\x01\x12\x14\n\x07timeout\x18\x06 \x01(\x04H\x03\x88\x01\x01\x42\x08\n\x06_exactB\x13\n\x11_read_consistencyB\x15\n\x13_shard_key_selectorB\n\n\x08_timeout\"\x9d\x01\n\x0eRecommendInput\x12%\n\x08positive\x18\x01 \x03(\x0b\x32\x13.qdrant.VectorInput\x12%\n\x08negative\x18\x02 \x03(\x0b\x32\x13.qdrant.VectorInput\x12\x30\n\x08strategy\x18\x03 \x01(\x0e\x32\x19.qdrant.RecommendStrategyH\x00\x88\x01\x01\x42\x0b\n\t_strategy\"`\n\x10\x43ontextInputPair\x12%\n\x08positive\x18\x01 \x01(\x0b\x32\x13.qdrant.VectorInput\x12%\n\x08negative\x18\x02 \x01(\x0b\x32\x13.qdrant.VectorInput\"[\n\rDiscoverInput\x12#\n\x06target\x18\x01 \x01(\x0b\x32\x13.qdrant.VectorInput\x12%\n\x07\x63ontext\x18\x02 \x01(\x0b\x32\x14.qdrant.ContextInput\"7\n\x0c\x43ontextInput\x12\'\n\x05pairs\x18\x01 \x03(\x0b\x32\x18.qdrant.ContextInputPair\"\xa2\x01\n\x07\x46ormula\x12&\n\nexpression\x18\x01 \x01(\x0b\x32\x12.qdrant.Expression\x12/\n\x08\x64\x65\x66\x61ults\x18\x02 \x03(\x0b\x32\x1d.qdrant.Formula.DefaultsEntry\x1a>\n\rDefaultsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\"\xcc\x05\n\nExpression\x12\x12\n\x08\x63onstant\x18\x01 \x01(\x02H\x00\x12\x12\n\x08variable\x18\x02 \x01(\tH\x00\x12&\n\tcondition\x18\x03 \x01(\x0b\x32\x11.qdrant.ConditionH\x00\x12+\n\x0cgeo_distance\x18\x04 \x01(\x0b\x32\x13.qdrant.GeoDistanceH\x00\x12\x12\n\x08\x64\x61tetime\x18\x05 \x01(\tH\x00\x12\x16\n\x0c\x64\x61tetime_key\x18\x06 \x01(\tH\x00\x12&\n\x04mult\x18\x07 \x01(\x0b\x32\x16.qdrant.MultExpressionH\x00\x12$\n\x03sum\x18\x08 \x01(\x0b\x32\x15.qdrant.SumExpressionH\x00\x12$\n\x03\x64iv\x18\t \x01(\x0b\x32\x15.qdrant.DivExpressionH\x00\x12!\n\x03neg\x18\n \x01(\x0b\x32\x12.qdrant.ExpressionH\x00\x12!\n\x03\x61\x62s\x18\x0b \x01(\x0b\x32\x12.qdrant.ExpressionH\x00\x12\"\n\x04sqrt\x18\x0c \x01(\x0b\x32\x12.qdrant.ExpressionH\x00\x12$\n\x03pow\x18\r \x01(\x0b\x32\x15.qdrant.PowExpressionH\x00\x12!\n\x03\x65xp\x18\x0e \x01(\x0b\x32\x12.qdrant.ExpressionH\x00\x12#\n\x05log10\x18\x0f \x01(\x0b\x32\x12.qdrant.ExpressionH\x00\x12 \n\x02ln\x18\x10 \x01(\x0b\x32\x12.qdrant.ExpressionH\x00\x12\x32\n\texp_decay\x18\x11 \x01(\x0b\x32\x1d.qdrant.DecayParamsExpressionH\x00\x12\x34\n\x0bgauss_decay\x18\x12 \x01(\x0b\x32\x1d.qdrant.DecayParamsExpressionH\x00\x12\x32\n\tlin_decay\x18\x13 \x01(\x0b\x32\x1d.qdrant.DecayParamsExpressionH\x00\x42\t\n\x07variant\";\n\x0bGeoDistance\x12 \n\x06origin\x18\x01 \x01(\x0b\x32\x10.qdrant.GeoPoint\x12\n\n\x02to\x18\x02 \x01(\t\"2\n\x0eMultExpression\x12 \n\x04mult\x18\x01 \x03(\x0b\x32\x12.qdrant.Expression\"0\n\rSumExpression\x12\x1f\n\x03sum\x18\x01 \x03(\x0b\x32\x12.qdrant.Expression\"\x86\x01\n\rDivExpression\x12 \n\x04left\x18\x01 \x01(\x0b\x32\x12.qdrant.Expression\x12!\n\x05right\x18\x02 \x01(\x0b\x32\x12.qdrant.Expression\x12\x1c\n\x0f\x62y_zero_default\x18\x03 \x01(\x02H\x00\x88\x01\x01\x42\x12\n\x10_by_zero_default\"W\n\rPowExpression\x12 \n\x04\x62\x61se\x18\x01 \x01(\x0b\x32\x12.qdrant.Expression\x12$\n\x08\x65xponent\x18\x02 \x01(\x0b\x32\x12.qdrant.Expression\"\xac\x01\n\x15\x44\x65\x63\x61yParamsExpression\x12\x1d\n\x01x\x18\x01 \x01(\x0b\x32\x12.qdrant.Expression\x12\'\n\x06target\x18\x02 \x01(\x0b\x32\x12.qdrant.ExpressionH\x00\x88\x01\x01\x12\x12\n\x05scale\x18\x03 \x01(\x02H\x01\x88\x01\x01\x12\x15\n\x08midpoint\x18\x04 \x01(\x02H\x02\x88\x01\x01\x42\t\n\x07_targetB\x08\n\x06_scaleB\x0b\n\t_midpoint\"\xc8\x02\n\x05Query\x12&\n\x07nearest\x18\x01 \x01(\x0b\x32\x13.qdrant.VectorInputH\x00\x12+\n\trecommend\x18\x02 \x01(\x0b\x32\x16.qdrant.RecommendInputH\x00\x12)\n\x08\x64iscover\x18\x03 \x01(\x0b\x32\x15.qdrant.DiscoverInputH\x00\x12\'\n\x07\x63ontext\x18\x04 \x01(\x0b\x32\x14.qdrant.ContextInputH\x00\x12#\n\x08order_by\x18\x05 \x01(\x0b\x32\x0f.qdrant.OrderByH\x00\x12 \n\x06\x66usion\x18\x06 \x01(\x0e\x32\x0e.qdrant.FusionH\x00\x12 \n\x06sample\x18\x07 \x01(\x0e\x32\x0e.qdrant.SampleH\x00\x12\"\n\x07\x66ormula\x18\x08 \x01(\x0b\x32\x0f.qdrant.FormulaH\x00\x42\t\n\x07variant\"\xfb\x02\n\rPrefetchQuery\x12\'\n\x08prefetch\x18\x01 \x03(\x0b\x32\x15.qdrant.PrefetchQuery\x12!\n\x05query\x18\x02 \x01(\x0b\x32\r.qdrant.QueryH\x00\x88\x01\x01\x12\x12\n\x05using\x18\x03 \x01(\tH\x01\x88\x01\x01\x12#\n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x0e.qdrant.FilterH\x02\x88\x01\x01\x12)\n\x06params\x18\x05 \x01(\x0b\x32\x14.qdrant.SearchParamsH\x03\x88\x01\x01\x12\x1c\n\x0fscore_threshold\x18\x06 \x01(\x02H\x04\x88\x01\x01\x12\x12\n\x05limit\x18\x07 \x01(\x04H\x05\x88\x01\x01\x12\x30\n\x0blookup_from\x18\x08 \x01(\x0b\x32\x16.qdrant.LookupLocationH\x06\x88\x01\x01\x42\x08\n\x06_queryB\x08\n\x06_usingB\t\n\x07_filterB\t\n\x07_paramsB\x12\n\x10_score_thresholdB\x08\n\x06_limitB\x0e\n\x0c_lookup_from\"\x85\x06\n\x0bQueryPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\'\n\x08prefetch\x18\x02 \x03(\x0b\x32\x15.qdrant.PrefetchQuery\x12!\n\x05query\x18\x03 \x01(\x0b\x32\r.qdrant.QueryH\x00\x88\x01\x01\x12\x12\n\x05using\x18\x04 \x01(\tH\x01\x88\x01\x01\x12#\n\x06\x66ilter\x18\x05 \x01(\x0b\x32\x0e.qdrant.FilterH\x02\x88\x01\x01\x12)\n\x06params\x18\x06 \x01(\x0b\x32\x14.qdrant.SearchParamsH\x03\x88\x01\x01\x12\x1c\n\x0fscore_threshold\x18\x07 \x01(\x02H\x04\x88\x01\x01\x12\x12\n\x05limit\x18\x08 \x01(\x04H\x05\x88\x01\x01\x12\x13\n\x06offset\x18\t \x01(\x04H\x06\x88\x01\x01\x12\x36\n\x0cwith_vectors\x18\n \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x07\x88\x01\x01\x12\x36\n\x0cwith_payload\x18\x0b \x01(\x0b\x32\x1b.qdrant.WithPayloadSelectorH\x08\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x0c \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\t\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\r \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\n\x88\x01\x01\x12\x30\n\x0blookup_from\x18\x0e \x01(\x0b\x32\x16.qdrant.LookupLocationH\x0b\x88\x01\x01\x12\x14\n\x07timeout\x18\x0f \x01(\x04H\x0c\x88\x01\x01\x42\x08\n\x06_queryB\x08\n\x06_usingB\t\n\x07_filterB\t\n\x07_paramsB\x12\n\x10_score_thresholdB\x08\n\x06_limitB\t\n\x07_offsetB\x0f\n\r_with_vectorsB\x0f\n\r_with_payloadB\x13\n\x11_read_consistencyB\x15\n\x13_shard_key_selectorB\x0e\n\x0c_lookup_fromB\n\n\x08_timeout\"\xc5\x01\n\x10QueryBatchPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12)\n\x0cquery_points\x18\x02 \x03(\x0b\x32\x13.qdrant.QueryPoints\x12\x36\n\x10read_consistency\x18\x03 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x00\x88\x01\x01\x12\x14\n\x07timeout\x18\x04 \x01(\x04H\x01\x88\x01\x01\x42\x13\n\x11_read_consistencyB\n\n\x08_timeout\"\xcc\x06\n\x10QueryPointGroups\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\'\n\x08prefetch\x18\x02 \x03(\x0b\x32\x15.qdrant.PrefetchQuery\x12!\n\x05query\x18\x03 \x01(\x0b\x32\r.qdrant.QueryH\x00\x88\x01\x01\x12\x12\n\x05using\x18\x04 \x01(\tH\x01\x88\x01\x01\x12#\n\x06\x66ilter\x18\x05 \x01(\x0b\x32\x0e.qdrant.FilterH\x02\x88\x01\x01\x12)\n\x06params\x18\x06 \x01(\x0b\x32\x14.qdrant.SearchParamsH\x03\x88\x01\x01\x12\x1c\n\x0fscore_threshold\x18\x07 \x01(\x02H\x04\x88\x01\x01\x12\x31\n\x0cwith_payload\x18\x08 \x01(\x0b\x32\x1b.qdrant.WithPayloadSelector\x12\x36\n\x0cwith_vectors\x18\t \x01(\x0b\x32\x1b.qdrant.WithVectorsSelectorH\x05\x88\x01\x01\x12\x30\n\x0blookup_from\x18\n \x01(\x0b\x32\x16.qdrant.LookupLocationH\x06\x88\x01\x01\x12\x12\n\x05limit\x18\x0b \x01(\x04H\x07\x88\x01\x01\x12\x17\n\ngroup_size\x18\x0c \x01(\x04H\x08\x88\x01\x01\x12\x10\n\x08group_by\x18\r \x01(\t\x12\x36\n\x10read_consistency\x18\x0e \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\t\x88\x01\x01\x12,\n\x0bwith_lookup\x18\x0f \x01(\x0b\x32\x12.qdrant.WithLookupH\n\x88\x01\x01\x12\x14\n\x07timeout\x18\x10 \x01(\x04H\x0b\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x11 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x0c\x88\x01\x01\x42\x08\n\x06_queryB\x08\n\x06_usingB\t\n\x07_filterB\t\n\x07_paramsB\x12\n\x10_score_thresholdB\x0f\n\r_with_vectorsB\x0e\n\x0c_lookup_fromB\x08\n\x06_limitB\r\n\x0b_group_sizeB\x13\n\x11_read_consistencyB\x0e\n\x0c_with_lookupB\n\n\x08_timeoutB\x15\n\x13_shard_key_selector\"\xe0\x02\n\x0b\x46\x61\x63\x65tCounts\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12#\n\x06\x66ilter\x18\x03 \x01(\x0b\x32\x0e.qdrant.FilterH\x00\x88\x01\x01\x12\x12\n\x05limit\x18\x04 \x01(\x04H\x01\x88\x01\x01\x12\x12\n\x05\x65xact\x18\x05 \x01(\x08H\x02\x88\x01\x01\x12\x14\n\x07timeout\x18\x06 \x01(\x04H\x03\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x07 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x04\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x08 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x05\x88\x01\x01\x42\t\n\x07_filterB\x08\n\x06_limitB\x08\n\x06_exactB\n\n\x08_timeoutB\x13\n\x11_read_consistencyB\x15\n\x13_shard_key_selector\"^\n\nFacetValue\x12\x16\n\x0cstring_value\x18\x01 \x01(\tH\x00\x12\x17\n\rinteger_value\x18\x02 \x01(\x03H\x00\x12\x14\n\nbool_value\x18\x03 \x01(\x08H\x00\x42\t\n\x07variant\"<\n\x08\x46\x61\x63\x65tHit\x12!\n\x05value\x18\x01 \x01(\x0b\x32\x12.qdrant.FacetValue\x12\r\n\x05\x63ount\x18\x02 \x01(\x04\"\xfa\x02\n\x12SearchMatrixPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12#\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x0e.qdrant.FilterH\x00\x88\x01\x01\x12\x13\n\x06sample\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x12\n\x05limit\x18\x04 \x01(\x04H\x02\x88\x01\x01\x12\x12\n\x05using\x18\x05 \x01(\tH\x03\x88\x01\x01\x12\x14\n\x07timeout\x18\x06 \x01(\x04H\x04\x88\x01\x01\x12\x36\n\x10read_consistency\x18\x07 \x01(\x0b\x32\x17.qdrant.ReadConsistencyH\x05\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x08 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x06\x88\x01\x01\x42\t\n\x07_filterB\t\n\x07_sampleB\x08\n\x06_limitB\x08\n\x06_usingB\n\n\x08_timeoutB\x13\n\x11_read_consistencyB\x15\n\x13_shard_key_selector\"<\n\x11SearchMatrixPairs\x12\'\n\x05pairs\x18\x01 \x03(\x0b\x32\x18.qdrant.SearchMatrixPair\"Y\n\x10SearchMatrixPair\x12\x1a\n\x01\x61\x18\x01 \x01(\x0b\x32\x0f.qdrant.PointId\x12\x1a\n\x01\x62\x18\x02 \x01(\x0b\x32\x0f.qdrant.PointId\x12\r\n\x05score\x18\x03 \x01(\x02\"m\n\x13SearchMatrixOffsets\x12\x13\n\x0boffsets_row\x18\x01 \x03(\x04\x12\x13\n\x0boffsets_col\x18\x02 \x03(\x04\x12\x0e\n\x06scores\x18\x03 \x03(\x02\x12\x1c\n\x03ids\x18\x04 \x03(\x0b\x32\x0f.qdrant.PointId\"\x95\x12\n\x15PointsUpdateOperation\x12?\n\x06upsert\x18\x01 \x01(\x0b\x32-.qdrant.PointsUpdateOperation.PointStructListH\x00\x12\x37\n\x11\x64\x65lete_deprecated\x18\x02 \x01(\x0b\x32\x16.qdrant.PointsSelectorB\x02\x18\x01H\x00\x12?\n\x0bset_payload\x18\x03 \x01(\x0b\x32(.qdrant.PointsUpdateOperation.SetPayloadH\x00\x12K\n\x11overwrite_payload\x18\x04 \x01(\x0b\x32..qdrant.PointsUpdateOperation.OverwritePayloadH\x00\x12\x45\n\x0e\x64\x65lete_payload\x18\x05 \x01(\x0b\x32+.qdrant.PointsUpdateOperation.DeletePayloadH\x00\x12>\n\x18\x63lear_payload_deprecated\x18\x06 \x01(\x0b\x32\x16.qdrant.PointsSelectorB\x02\x18\x01H\x00\x12\x45\n\x0eupdate_vectors\x18\x07 \x01(\x0b\x32+.qdrant.PointsUpdateOperation.UpdateVectorsH\x00\x12\x45\n\x0e\x64\x65lete_vectors\x18\x08 \x01(\x0b\x32+.qdrant.PointsUpdateOperation.DeleteVectorsH\x00\x12\x43\n\rdelete_points\x18\t \x01(\x0b\x32*.qdrant.PointsUpdateOperation.DeletePointsH\x00\x12\x43\n\rclear_payload\x18\n \x01(\x0b\x32*.qdrant.PointsUpdateOperation.ClearPayloadH\x00\x1a\x88\x01\n\x0fPointStructList\x12#\n\x06points\x18\x01 \x03(\x0b\x32\x13.qdrant.PointStruct\x12\x39\n\x12shard_key_selector\x18\x02 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x00\x88\x01\x01\x42\x15\n\x13_shard_key_selector\x1a\xc9\x02\n\nSetPayload\x12\x46\n\x07payload\x18\x01 \x03(\x0b\x32\x35.qdrant.PointsUpdateOperation.SetPayload.PayloadEntry\x12\x34\n\x0fpoints_selector\x18\x02 \x01(\x0b\x32\x16.qdrant.PointsSelectorH\x00\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x03 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x01\x88\x01\x01\x12\x10\n\x03key\x18\x04 \x01(\tH\x02\x88\x01\x01\x1a=\n\x0cPayloadEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\x42\x12\n\x10_points_selectorB\x15\n\x13_shard_key_selectorB\x06\n\x04_key\x1a\xd5\x02\n\x10OverwritePayload\x12L\n\x07payload\x18\x01 \x03(\x0b\x32;.qdrant.PointsUpdateOperation.OverwritePayload.PayloadEntry\x12\x34\n\x0fpoints_selector\x18\x02 \x01(\x0b\x32\x16.qdrant.PointsSelectorH\x00\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x03 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x01\x88\x01\x01\x12\x10\n\x03key\x18\x04 \x01(\tH\x02\x88\x01\x01\x1a=\n\x0cPayloadEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\x42\x12\n\x10_points_selectorB\x15\n\x13_shard_key_selectorB\x06\n\x04_key\x1a\xb9\x01\n\rDeletePayload\x12\x0c\n\x04keys\x18\x01 \x03(\t\x12\x34\n\x0fpoints_selector\x18\x02 \x01(\x0b\x32\x16.qdrant.PointsSelectorH\x00\x88\x01\x01\x12\x39\n\x12shard_key_selector\x18\x03 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x01\x88\x01\x01\x42\x12\n\x10_points_selectorB\x15\n\x13_shard_key_selector\x1a\x87\x01\n\rUpdateVectors\x12$\n\x06points\x18\x01 \x03(\x0b\x32\x14.qdrant.PointVectors\x12\x39\n\x12shard_key_selector\x18\x02 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x00\x88\x01\x01\x42\x15\n\x13_shard_key_selector\x1a\xbc\x01\n\rDeleteVectors\x12/\n\x0fpoints_selector\x18\x01 \x01(\x0b\x32\x16.qdrant.PointsSelector\x12(\n\x07vectors\x18\x02 \x01(\x0b\x32\x17.qdrant.VectorsSelector\x12\x39\n\x12shard_key_selector\x18\x03 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x00\x88\x01\x01\x42\x15\n\x13_shard_key_selector\x1a\x88\x01\n\x0c\x44\x65letePoints\x12&\n\x06points\x18\x01 \x01(\x0b\x32\x16.qdrant.PointsSelector\x12\x39\n\x12shard_key_selector\x18\x02 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x00\x88\x01\x01\x42\x15\n\x13_shard_key_selector\x1a\x88\x01\n\x0c\x43learPayload\x12&\n\x06points\x18\x01 \x01(\x0b\x32\x16.qdrant.PointsSelector\x12\x39\n\x12shard_key_selector\x18\x02 \x01(\x0b\x32\x18.qdrant.ShardKeySelectorH\x00\x88\x01\x01\x42\x15\n\x13_shard_key_selectorB\x0b\n\toperation\"\xb6\x01\n\x11UpdateBatchPoints\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x11\n\x04wait\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x31\n\noperations\x18\x03 \x03(\x0b\x32\x1d.qdrant.PointsUpdateOperation\x12,\n\x08ordering\x18\x04 \x01(\x0b\x32\x15.qdrant.WriteOrderingH\x01\x88\x01\x01\x42\x07\n\x05_waitB\x0b\n\t_ordering\"\x82\x01\n\x17PointsOperationResponse\x12$\n\x06result\x18\x01 \x01(\x0b\x32\x14.qdrant.UpdateResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"`\n\x0cUpdateResult\x12\x19\n\x0coperation_id\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12$\n\x06status\x18\x02 \x01(\x0e\x32\x14.qdrant.UpdateStatusB\x0f\n\r_operation_id\"7\n\nOrderValue\x12\r\n\x03int\x18\x01 \x01(\x03H\x00\x12\x0f\n\x05\x66loat\x18\x02 \x01(\x01H\x00\x42\t\n\x07variant\"\xf1\x02\n\x0bScoredPoint\x12\x1b\n\x02id\x18\x01 \x01(\x0b\x32\x0f.qdrant.PointId\x12\x31\n\x07payload\x18\x02 \x03(\x0b\x32 .qdrant.ScoredPoint.PayloadEntry\x12\r\n\x05score\x18\x03 \x01(\x02\x12\x0f\n\x07version\x18\x05 \x01(\x04\x12+\n\x07vectors\x18\x06 \x01(\x0b\x32\x15.qdrant.VectorsOutputH\x00\x88\x01\x01\x12(\n\tshard_key\x18\x07 \x01(\x0b\x32\x10.qdrant.ShardKeyH\x01\x88\x01\x01\x12,\n\x0border_value\x18\x08 \x01(\x0b\x32\x12.qdrant.OrderValueH\x02\x88\x01\x01\x1a=\n\x0cPayloadEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\x42\n\n\x08_vectorsB\x0c\n\n_shard_keyB\x0e\n\x0c_order_valueJ\x04\x08\x04\x10\x05\"\\\n\x07GroupId\x12\x18\n\x0eunsigned_value\x18\x01 \x01(\x04H\x00\x12\x17\n\rinteger_value\x18\x02 \x01(\x03H\x00\x12\x16\n\x0cstring_value\x18\x03 \x01(\tH\x00\x42\x06\n\x04kind\"t\n\nPointGroup\x12\x1b\n\x02id\x18\x01 \x01(\x0b\x32\x0f.qdrant.GroupId\x12!\n\x04hits\x18\x02 \x03(\x0b\x32\x13.qdrant.ScoredPoint\x12&\n\x06lookup\x18\x03 \x01(\x0b\x32\x16.qdrant.RetrievedPoint\"2\n\x0cGroupsResult\x12\"\n\x06groups\x18\x01 \x03(\x0b\x32\x12.qdrant.PointGroup\"x\n\x0eSearchResponse\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.ScoredPoint\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"w\n\rQueryResponse\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.ScoredPoint\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"|\n\x12QueryBatchResponse\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.BatchResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"~\n\x13QueryGroupsResponse\x12$\n\x06result\x18\x01 \x01(\x0b\x32\x14.qdrant.GroupsResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"2\n\x0b\x42\x61tchResult\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.ScoredPoint\"}\n\x13SearchBatchResponse\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.BatchResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"\x7f\n\x14SearchGroupsResponse\x12$\n\x06result\x18\x01 \x01(\x0b\x32\x14.qdrant.GroupsResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"w\n\rCountResponse\x12#\n\x06result\x18\x01 \x01(\x0b\x32\x13.qdrant.CountResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"\xc0\x01\n\x0eScrollResponse\x12.\n\x10next_page_offset\x18\x01 \x01(\x0b\x32\x0f.qdrant.PointIdH\x00\x88\x01\x01\x12&\n\x06result\x18\x02 \x03(\x0b\x32\x16.qdrant.RetrievedPoint\x12\x0c\n\x04time\x18\x03 \x01(\x01\x12)\n\x05usage\x18\x04 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x01\x88\x01\x01\x42\x13\n\x11_next_page_offsetB\x08\n\x06_usage\"\x1c\n\x0b\x43ountResult\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\"\xd7\x02\n\x0eRetrievedPoint\x12\x1b\n\x02id\x18\x01 \x01(\x0b\x32\x0f.qdrant.PointId\x12\x34\n\x07payload\x18\x02 \x03(\x0b\x32#.qdrant.RetrievedPoint.PayloadEntry\x12+\n\x07vectors\x18\x04 \x01(\x0b\x32\x15.qdrant.VectorsOutputH\x00\x88\x01\x01\x12(\n\tshard_key\x18\x05 \x01(\x0b\x32\x10.qdrant.ShardKeyH\x01\x88\x01\x01\x12,\n\x0border_value\x18\x06 \x01(\x0b\x32\x12.qdrant.OrderValueH\x02\x88\x01\x01\x1a=\n\x0cPayloadEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\x42\n\n\x08_vectorsB\x0c\n\n_shard_keyB\x0e\n\x0c_order_valueJ\x04\x08\x03\x10\x04\"x\n\x0bGetResponse\x12&\n\x06result\x18\x01 \x03(\x0b\x32\x16.qdrant.RetrievedPoint\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"{\n\x11RecommendResponse\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.ScoredPoint\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"\x80\x01\n\x16RecommendBatchResponse\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.BatchResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"z\n\x10\x44iscoverResponse\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.ScoredPoint\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"\x7f\n\x15\x44iscoverBatchResponse\x12#\n\x06result\x18\x01 \x03(\x0b\x32\x13.qdrant.BatchResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"\x82\x01\n\x17RecommendGroupsResponse\x12$\n\x06result\x18\x01 \x01(\x0b\x32\x14.qdrant.GroupsResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"I\n\x13UpdateBatchResponse\x12$\n\x06result\x18\x01 \x03(\x0b\x32\x14.qdrant.UpdateResult\x12\x0c\n\x04time\x18\x02 \x01(\x01\"=\n\rFacetResponse\x12\x1e\n\x04hits\x18\x01 \x03(\x0b\x32\x10.qdrant.FacetHit\x12\x0c\n\x04time\x18\x02 \x01(\x01\"\x89\x01\n\x19SearchMatrixPairsResponse\x12)\n\x06result\x18\x01 \x01(\x0b\x32\x19.qdrant.SearchMatrixPairs\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"\x8d\x01\n\x1bSearchMatrixOffsetsResponse\x12+\n\x06result\x18\x01 \x01(\x0b\x32\x1b.qdrant.SearchMatrixOffsets\x12\x0c\n\x04time\x18\x02 \x01(\x01\x12)\n\x05usage\x18\x03 \x01(\x0b\x32\x15.qdrant.HardwareUsageH\x00\x88\x01\x01\x42\x08\n\x06_usage\"\xac\x01\n\x06\x46ilter\x12!\n\x06should\x18\x01 \x03(\x0b\x32\x11.qdrant.Condition\x12\x1f\n\x04must\x18\x02 \x03(\x0b\x32\x11.qdrant.Condition\x12#\n\x08must_not\x18\x03 \x03(\x0b\x32\x11.qdrant.Condition\x12*\n\nmin_should\x18\x04 \x01(\x0b\x32\x11.qdrant.MinShouldH\x00\x88\x01\x01\x42\r\n\x0b_min_should\"E\n\tMinShould\x12%\n\nconditions\x18\x01 \x03(\x0b\x32\x11.qdrant.Condition\x12\x11\n\tmin_count\x18\x02 \x01(\x04\"\xcb\x02\n\tCondition\x12\'\n\x05\x66ield\x18\x01 \x01(\x0b\x32\x16.qdrant.FieldConditionH\x00\x12,\n\x08is_empty\x18\x02 \x01(\x0b\x32\x18.qdrant.IsEmptyConditionH\x00\x12(\n\x06has_id\x18\x03 \x01(\x0b\x32\x16.qdrant.HasIdConditionH\x00\x12 \n\x06\x66ilter\x18\x04 \x01(\x0b\x32\x0e.qdrant.FilterH\x00\x12*\n\x07is_null\x18\x05 \x01(\x0b\x32\x17.qdrant.IsNullConditionH\x00\x12)\n\x06nested\x18\x06 \x01(\x0b\x32\x17.qdrant.NestedConditionH\x00\x12\x30\n\nhas_vector\x18\x07 \x01(\x0b\x32\x1a.qdrant.HasVectorConditionH\x00\x42\x12\n\x10\x63ondition_one_of\"\x1f\n\x10IsEmptyCondition\x12\x0b\n\x03key\x18\x01 \x01(\t\"\x1e\n\x0fIsNullCondition\x12\x0b\n\x03key\x18\x01 \x01(\t\"1\n\x0eHasIdCondition\x12\x1f\n\x06has_id\x18\x01 \x03(\x0b\x32\x0f.qdrant.PointId\"(\n\x12HasVectorCondition\x12\x12\n\nhas_vector\x18\x01 \x01(\t\">\n\x0fNestedCondition\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1e\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x0e.qdrant.Filter\"\xfb\x02\n\x0e\x46ieldCondition\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05match\x18\x02 \x01(\x0b\x32\r.qdrant.Match\x12\x1c\n\x05range\x18\x03 \x01(\x0b\x32\r.qdrant.Range\x12\x30\n\x10geo_bounding_box\x18\x04 \x01(\x0b\x32\x16.qdrant.GeoBoundingBox\x12%\n\ngeo_radius\x18\x05 \x01(\x0b\x32\x11.qdrant.GeoRadius\x12)\n\x0cvalues_count\x18\x06 \x01(\x0b\x32\x13.qdrant.ValuesCount\x12\'\n\x0bgeo_polygon\x18\x07 \x01(\x0b\x32\x12.qdrant.GeoPolygon\x12-\n\x0e\x64\x61tetime_range\x18\x08 \x01(\x0b\x32\x15.qdrant.DatetimeRange\x12\x15\n\x08is_empty\x18\t \x01(\x08H\x00\x88\x01\x01\x12\x14\n\x07is_null\x18\n \x01(\x08H\x01\x88\x01\x01\x42\x0b\n\t_is_emptyB\n\n\x08_is_null\"\xa3\x02\n\x05Match\x12\x11\n\x07keyword\x18\x01 \x01(\tH\x00\x12\x11\n\x07integer\x18\x02 \x01(\x03H\x00\x12\x11\n\x07\x62oolean\x18\x03 \x01(\x08H\x00\x12\x0e\n\x04text\x18\x04 \x01(\tH\x00\x12+\n\x08keywords\x18\x05 \x01(\x0b\x32\x17.qdrant.RepeatedStringsH\x00\x12,\n\x08integers\x18\x06 \x01(\x0b\x32\x18.qdrant.RepeatedIntegersH\x00\x12\x33\n\x0f\x65xcept_integers\x18\x07 \x01(\x0b\x32\x18.qdrant.RepeatedIntegersH\x00\x12\x32\n\x0f\x65xcept_keywords\x18\x08 \x01(\x0b\x32\x17.qdrant.RepeatedStringsH\x00\x42\r\n\x0bmatch_value\"\"\n\x0fRepeatedStrings\x12\x0f\n\x07strings\x18\x01 \x03(\t\"$\n\x10RepeatedIntegers\x12\x10\n\x08integers\x18\x01 \x03(\x03\"k\n\x05Range\x12\x0f\n\x02lt\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x0f\n\x02gt\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x10\n\x03gte\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x10\n\x03lte\x18\x04 \x01(\x01H\x03\x88\x01\x01\x42\x05\n\x03_ltB\x05\n\x03_gtB\x06\n\x04_gteB\x06\n\x04_lte\"\xe3\x01\n\rDatetimeRange\x12+\n\x02lt\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x00\x88\x01\x01\x12+\n\x02gt\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x01\x88\x01\x01\x12,\n\x03gte\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x02\x88\x01\x01\x12,\n\x03lte\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x03\x88\x01\x01\x42\x05\n\x03_ltB\x05\n\x03_gtB\x06\n\x04_gteB\x06\n\x04_lte\"\\\n\x0eGeoBoundingBox\x12\"\n\x08top_left\x18\x01 \x01(\x0b\x32\x10.qdrant.GeoPoint\x12&\n\x0c\x62ottom_right\x18\x02 \x01(\x0b\x32\x10.qdrant.GeoPoint\"=\n\tGeoRadius\x12 \n\x06\x63\x65nter\x18\x01 \x01(\x0b\x32\x10.qdrant.GeoPoint\x12\x0e\n\x06radius\x18\x02 \x01(\x02\"1\n\rGeoLineString\x12 \n\x06points\x18\x01 \x03(\x0b\x32\x10.qdrant.GeoPoint\"_\n\nGeoPolygon\x12\'\n\x08\x65xterior\x18\x01 \x01(\x0b\x32\x15.qdrant.GeoLineString\x12(\n\tinteriors\x18\x02 \x03(\x0b\x32\x15.qdrant.GeoLineString\"q\n\x0bValuesCount\x12\x0f\n\x02lt\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x0f\n\x02gt\x18\x02 \x01(\x04H\x01\x88\x01\x01\x12\x10\n\x03gte\x18\x03 \x01(\x04H\x02\x88\x01\x01\x12\x10\n\x03lte\x18\x04 \x01(\x04H\x03\x88\x01\x01\x42\x05\n\x03_ltB\x05\n\x03_gtB\x06\n\x04_gteB\x06\n\x04_lte\"u\n\x0ePointsSelector\x12\'\n\x06points\x18\x01 \x01(\x0b\x32\x15.qdrant.PointsIdsListH\x00\x12 \n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x0e.qdrant.FilterH\x00\x42\x18\n\x16points_selector_one_of\"-\n\rPointsIdsList\x12\x1c\n\x03ids\x18\x01 \x03(\x0b\x32\x0f.qdrant.PointId\"\xd5\x01\n\x0bPointStruct\x12\x1b\n\x02id\x18\x01 \x01(\x0b\x32\x0f.qdrant.PointId\x12\x31\n\x07payload\x18\x03 \x03(\x0b\x32 .qdrant.PointStruct.PayloadEntry\x12%\n\x07vectors\x18\x04 \x01(\x0b\x32\x0f.qdrant.VectorsH\x00\x88\x01\x01\x1a=\n\x0cPayloadEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x1c\n\x05value\x18\x02 \x01(\x0b\x32\r.qdrant.Value:\x02\x38\x01\x42\n\n\x08_vectorsJ\x04\x08\x02\x10\x03\"$\n\x08GeoPoint\x12\x0b\n\x03lon\x18\x01 \x01(\x01\x12\x0b\n\x03lat\x18\x02 \x01(\x01\"\xbf\x01\n\rHardwareUsage\x12\x0b\n\x03\x63pu\x18\x01 \x01(\x04\x12\x17\n\x0fpayload_io_read\x18\x02 \x01(\x04\x12\x18\n\x10payload_io_write\x18\x03 \x01(\x04\x12\x1d\n\x15payload_index_io_read\x18\x04 \x01(\x04\x12\x1e\n\x16payload_index_io_write\x18\x05 \x01(\x04\x12\x16\n\x0evector_io_read\x18\x06 \x01(\x04\x12\x17\n\x0fvector_io_write\x18\x07 \x01(\x04*5\n\x11WriteOrderingType\x12\x08\n\x04Weak\x10\x00\x12\n\n\x06Medium\x10\x01\x12\n\n\x06Strong\x10\x02*8\n\x13ReadConsistencyType\x12\x07\n\x03\x41ll\x10\x00\x12\x0c\n\x08Majority\x10\x01\x12\n\n\x06Quorum\x10\x02*\xad\x01\n\tFieldType\x12\x14\n\x10\x46ieldTypeKeyword\x10\x00\x12\x14\n\x10\x46ieldTypeInteger\x10\x01\x12\x12\n\x0e\x46ieldTypeFloat\x10\x02\x12\x10\n\x0c\x46ieldTypeGeo\x10\x03\x12\x11\n\rFieldTypeText\x10\x04\x12\x11\n\rFieldTypeBool\x10\x05\x12\x15\n\x11\x46ieldTypeDatetime\x10\x06\x12\x11\n\rFieldTypeUuid\x10\x07*\x1e\n\tDirection\x12\x07\n\x03\x41sc\x10\x00\x12\x08\n\x04\x44\x65sc\x10\x01*D\n\x11RecommendStrategy\x12\x11\n\rAverageVector\x10\x00\x12\r\n\tBestScore\x10\x01\x12\r\n\tSumScores\x10\x02*\x1b\n\x06\x46usion\x12\x07\n\x03RRF\x10\x00\x12\x08\n\x04\x44\x42SF\x10\x01*\x14\n\x06Sample\x12\n\n\x06Random\x10\x00*[\n\x0cUpdateStatus\x12\x17\n\x13UnknownUpdateStatus\x10\x00\x12\x10\n\x0c\x41\x63knowledged\x10\x01\x12\r\n\tCompleted\x10\x02\x12\x11\n\rClockRejected\x10\x03\x42\x15\xaa\x02\x12Qdrant.Client.Grpcb\x06proto3')

_WRITEORDERINGTYPE = DESCRIPTOR.enum_types_by_name['WriteOrderingType']
WriteOrderingType = enum_type_wrapper.EnumTypeWrapper(_WRITEORDERINGTYPE)
_READCONSISTENCYTYPE = DESCRIPTOR.enum_types_by_name['ReadConsistencyType']
ReadConsistencyType = enum_type_wrapper.EnumTypeWrapper(_READCONSISTENCYTYPE)
_FIELDTYPE = DESCRIPTOR.enum_types_by_name['FieldType']
FieldType = enum_type_wrapper.EnumTypeWrapper(_FIELDTYPE)
_DIRECTION = DESCRIPTOR.enum_types_by_name['Direction']
Direction = enum_type_wrapper.EnumTypeWrapper(_DIRECTION)
_RECOMMENDSTRATEGY = DESCRIPTOR.enum_types_by_name['RecommendStrategy']
RecommendStrategy = enum_type_wrapper.EnumTypeWrapper(_RECOMMENDSTRATEGY)
_FUSION = DESCRIPTOR.enum_types_by_name['Fusion']
Fusion = enum_type_wrapper.EnumTypeWrapper(_FUSION)
_SAMPLE = DESCRIPTOR.enum_types_by_name['Sample']
Sample = enum_type_wrapper.EnumTypeWrapper(_SAMPLE)
_UPDATESTATUS = DESCRIPTOR.enum_types_by_name['UpdateStatus']
UpdateStatus = enum_type_wrapper.EnumTypeWrapper(_UPDATESTATUS)
Weak = 0
Medium = 1
Strong = 2
All = 0
Majority = 1
Quorum = 2
FieldTypeKeyword = 0
FieldTypeInteger = 1
FieldTypeFloat = 2
FieldTypeGeo = 3
FieldTypeText = 4
FieldTypeBool = 5
FieldTypeDatetime = 6
FieldTypeUuid = 7
Asc = 0
Desc = 1
AverageVector = 0
BestScore = 1
SumScores = 2
RRF = 0
DBSF = 1
Random = 0
UnknownUpdateStatus = 0
Acknowledged = 1
Completed = 2
ClockRejected = 3


_WRITEORDERING = DESCRIPTOR.message_types_by_name['WriteOrdering']
_READCONSISTENCY = DESCRIPTOR.message_types_by_name['ReadConsistency']
_POINTID = DESCRIPTOR.message_types_by_name['PointId']
_SPARSEINDICES = DESCRIPTOR.message_types_by_name['SparseIndices']
_DOCUMENT = DESCRIPTOR.message_types_by_name['Document']
_DOCUMENT_OPTIONSENTRY = _DOCUMENT.nested_types_by_name['OptionsEntry']
_IMAGE = DESCRIPTOR.message_types_by_name['Image']
_IMAGE_OPTIONSENTRY = _IMAGE.nested_types_by_name['OptionsEntry']
_INFERENCEOBJECT = DESCRIPTOR.message_types_by_name['InferenceObject']
_INFERENCEOBJECT_OPTIONSENTRY = _INFERENCEOBJECT.nested_types_by_name['OptionsEntry']
_VECTOR = DESCRIPTOR.message_types_by_name['Vector']
_VECTOROUTPUT = DESCRIPTOR.message_types_by_name['VectorOutput']
_DENSEVECTOR = DESCRIPTOR.message_types_by_name['DenseVector']
_SPARSEVECTOR = DESCRIPTOR.message_types_by_name['SparseVector']
_MULTIDENSEVECTOR = DESCRIPTOR.message_types_by_name['MultiDenseVector']
_VECTORINPUT = DESCRIPTOR.message_types_by_name['VectorInput']
_SHARDKEYSELECTOR = DESCRIPTOR.message_types_by_name['ShardKeySelector']
_UPSERTPOINTS = DESCRIPTOR.message_types_by_name['UpsertPoints']
_DELETEPOINTS = DESCRIPTOR.message_types_by_name['DeletePoints']
_GETPOINTS = DESCRIPTOR.message_types_by_name['GetPoints']
_UPDATEPOINTVECTORS = DESCRIPTOR.message_types_by_name['UpdatePointVectors']
_POINTVECTORS = DESCRIPTOR.message_types_by_name['PointVectors']
_DELETEPOINTVECTORS = DESCRIPTOR.message_types_by_name['DeletePointVectors']
_SETPAYLOADPOINTS = DESCRIPTOR.message_types_by_name['SetPayloadPoints']
_SETPAYLOADPOINTS_PAYLOADENTRY = _SETPAYLOADPOINTS.nested_types_by_name['PayloadEntry']
_DELETEPAYLOADPOINTS = DESCRIPTOR.message_types_by_name['DeletePayloadPoints']
_CLEARPAYLOADPOINTS = DESCRIPTOR.message_types_by_name['ClearPayloadPoints']
_CREATEFIELDINDEXCOLLECTION = DESCRIPTOR.message_types_by_name['CreateFieldIndexCollection']
_DELETEFIELDINDEXCOLLECTION = DESCRIPTOR.message_types_by_name['DeleteFieldIndexCollection']
_PAYLOADINCLUDESELECTOR = DESCRIPTOR.message_types_by_name['PayloadIncludeSelector']
_PAYLOADEXCLUDESELECTOR = DESCRIPTOR.message_types_by_name['PayloadExcludeSelector']
_WITHPAYLOADSELECTOR = DESCRIPTOR.message_types_by_name['WithPayloadSelector']
_NAMEDVECTORS = DESCRIPTOR.message_types_by_name['NamedVectors']
_NAMEDVECTORS_VECTORSENTRY = _NAMEDVECTORS.nested_types_by_name['VectorsEntry']
_NAMEDVECTORSOUTPUT = DESCRIPTOR.message_types_by_name['NamedVectorsOutput']
_NAMEDVECTORSOUTPUT_VECTORSENTRY = _NAMEDVECTORSOUTPUT.nested_types_by_name['VectorsEntry']
_VECTORS = DESCRIPTOR.message_types_by_name['Vectors']
_VECTORSOUTPUT = DESCRIPTOR.message_types_by_name['VectorsOutput']
_VECTORSSELECTOR = DESCRIPTOR.message_types_by_name['VectorsSelector']
_WITHVECTORSSELECTOR = DESCRIPTOR.message_types_by_name['WithVectorsSelector']
_QUANTIZATIONSEARCHPARAMS = DESCRIPTOR.message_types_by_name['QuantizationSearchParams']
_SEARCHPARAMS = DESCRIPTOR.message_types_by_name['SearchParams']
_SEARCHPOINTS = DESCRIPTOR.message_types_by_name['SearchPoints']
_SEARCHBATCHPOINTS = DESCRIPTOR.message_types_by_name['SearchBatchPoints']
_WITHLOOKUP = DESCRIPTOR.message_types_by_name['WithLookup']
_SEARCHPOINTGROUPS = DESCRIPTOR.message_types_by_name['SearchPointGroups']
_STARTFROM = DESCRIPTOR.message_types_by_name['StartFrom']
_ORDERBY = DESCRIPTOR.message_types_by_name['OrderBy']
_SCROLLPOINTS = DESCRIPTOR.message_types_by_name['ScrollPoints']
_LOOKUPLOCATION = DESCRIPTOR.message_types_by_name['LookupLocation']
_RECOMMENDPOINTS = DESCRIPTOR.message_types_by_name['RecommendPoints']
_RECOMMENDBATCHPOINTS = DESCRIPTOR.message_types_by_name['RecommendBatchPoints']
_RECOMMENDPOINTGROUPS = DESCRIPTOR.message_types_by_name['RecommendPointGroups']
_TARGETVECTOR = DESCRIPTOR.message_types_by_name['TargetVector']
_VECTOREXAMPLE = DESCRIPTOR.message_types_by_name['VectorExample']
_CONTEXTEXAMPLEPAIR = DESCRIPTOR.message_types_by_name['ContextExamplePair']
_DISCOVERPOINTS = DESCRIPTOR.message_types_by_name['DiscoverPoints']
_DISCOVERBATCHPOINTS = DESCRIPTOR.message_types_by_name['DiscoverBatchPoints']
_COUNTPOINTS = DESCRIPTOR.message_types_by_name['CountPoints']
_RECOMMENDINPUT = DESCRIPTOR.message_types_by_name['RecommendInput']
_CONTEXTINPUTPAIR = DESCRIPTOR.message_types_by_name['ContextInputPair']
_DISCOVERINPUT = DESCRIPTOR.message_types_by_name['DiscoverInput']
_CONTEXTINPUT = DESCRIPTOR.message_types_by_name['ContextInput']
_FORMULA = DESCRIPTOR.message_types_by_name['Formula']
_FORMULA_DEFAULTSENTRY = _FORMULA.nested_types_by_name['DefaultsEntry']
_EXPRESSION = DESCRIPTOR.message_types_by_name['Expression']
_GEODISTANCE = DESCRIPTOR.message_types_by_name['GeoDistance']
_MULTEXPRESSION = DESCRIPTOR.message_types_by_name['MultExpression']
_SUMEXPRESSION = DESCRIPTOR.message_types_by_name['SumExpression']
_DIVEXPRESSION = DESCRIPTOR.message_types_by_name['DivExpression']
_POWEXPRESSION = DESCRIPTOR.message_types_by_name['PowExpression']
_DECAYPARAMSEXPRESSION = DESCRIPTOR.message_types_by_name['DecayParamsExpression']
_QUERY = DESCRIPTOR.message_types_by_name['Query']
_PREFETCHQUERY = DESCRIPTOR.message_types_by_name['PrefetchQuery']
_QUERYPOINTS = DESCRIPTOR.message_types_by_name['QueryPoints']
_QUERYBATCHPOINTS = DESCRIPTOR.message_types_by_name['QueryBatchPoints']
_QUERYPOINTGROUPS = DESCRIPTOR.message_types_by_name['QueryPointGroups']
_FACETCOUNTS = DESCRIPTOR.message_types_by_name['FacetCounts']
_FACETVALUE = DESCRIPTOR.message_types_by_name['FacetValue']
_FACETHIT = DESCRIPTOR.message_types_by_name['FacetHit']
_SEARCHMATRIXPOINTS = DESCRIPTOR.message_types_by_name['SearchMatrixPoints']
_SEARCHMATRIXPAIRS = DESCRIPTOR.message_types_by_name['SearchMatrixPairs']
_SEARCHMATRIXPAIR = DESCRIPTOR.message_types_by_name['SearchMatrixPair']
_SEARCHMATRIXOFFSETS = DESCRIPTOR.message_types_by_name['SearchMatrixOffsets']
_POINTSUPDATEOPERATION = DESCRIPTOR.message_types_by_name['PointsUpdateOperation']
_POINTSUPDATEOPERATION_POINTSTRUCTLIST = _POINTSUPDATEOPERATION.nested_types_by_name['PointStructList']
_POINTSUPDATEOPERATION_SETPAYLOAD = _POINTSUPDATEOPERATION.nested_types_by_name['SetPayload']
_POINTSUPDATEOPERATION_SETPAYLOAD_PAYLOADENTRY = _POINTSUPDATEOPERATION_SETPAYLOAD.nested_types_by_name['PayloadEntry']
_POINTSUPDATEOPERATION_OVERWRITEPAYLOAD = _POINTSUPDATEOPERATION.nested_types_by_name['OverwritePayload']
_POINTSUPDATEOPERATION_OVERWRITEPAYLOAD_PAYLOADENTRY = _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD.nested_types_by_name['PayloadEntry']
_POINTSUPDATEOPERATION_DELETEPAYLOAD = _POINTSUPDATEOPERATION.nested_types_by_name['DeletePayload']
_POINTSUPDATEOPERATION_UPDATEVECTORS = _POINTSUPDATEOPERATION.nested_types_by_name['UpdateVectors']
_POINTSUPDATEOPERATION_DELETEVECTORS = _POINTSUPDATEOPERATION.nested_types_by_name['DeleteVectors']
_POINTSUPDATEOPERATION_DELETEPOINTS = _POINTSUPDATEOPERATION.nested_types_by_name['DeletePoints']
_POINTSUPDATEOPERATION_CLEARPAYLOAD = _POINTSUPDATEOPERATION.nested_types_by_name['ClearPayload']
_UPDATEBATCHPOINTS = DESCRIPTOR.message_types_by_name['UpdateBatchPoints']
_POINTSOPERATIONRESPONSE = DESCRIPTOR.message_types_by_name['PointsOperationResponse']
_UPDATERESULT = DESCRIPTOR.message_types_by_name['UpdateResult']
_ORDERVALUE = DESCRIPTOR.message_types_by_name['OrderValue']
_SCOREDPOINT = DESCRIPTOR.message_types_by_name['ScoredPoint']
_SCOREDPOINT_PAYLOADENTRY = _SCOREDPOINT.nested_types_by_name['PayloadEntry']
_GROUPID = DESCRIPTOR.message_types_by_name['GroupId']
_POINTGROUP = DESCRIPTOR.message_types_by_name['PointGroup']
_GROUPSRESULT = DESCRIPTOR.message_types_by_name['GroupsResult']
_SEARCHRESPONSE = DESCRIPTOR.message_types_by_name['SearchResponse']
_QUERYRESPONSE = DESCRIPTOR.message_types_by_name['QueryResponse']
_QUERYBATCHRESPONSE = DESCRIPTOR.message_types_by_name['QueryBatchResponse']
_QUERYGROUPSRESPONSE = DESCRIPTOR.message_types_by_name['QueryGroupsResponse']
_BATCHRESULT = DESCRIPTOR.message_types_by_name['BatchResult']
_SEARCHBATCHRESPONSE = DESCRIPTOR.message_types_by_name['SearchBatchResponse']
_SEARCHGROUPSRESPONSE = DESCRIPTOR.message_types_by_name['SearchGroupsResponse']
_COUNTRESPONSE = DESCRIPTOR.message_types_by_name['CountResponse']
_SCROLLRESPONSE = DESCRIPTOR.message_types_by_name['ScrollResponse']
_COUNTRESULT = DESCRIPTOR.message_types_by_name['CountResult']
_RETRIEVEDPOINT = DESCRIPTOR.message_types_by_name['RetrievedPoint']
_RETRIEVEDPOINT_PAYLOADENTRY = _RETRIEVEDPOINT.nested_types_by_name['PayloadEntry']
_GETRESPONSE = DESCRIPTOR.message_types_by_name['GetResponse']
_RECOMMENDRESPONSE = DESCRIPTOR.message_types_by_name['RecommendResponse']
_RECOMMENDBATCHRESPONSE = DESCRIPTOR.message_types_by_name['RecommendBatchResponse']
_DISCOVERRESPONSE = DESCRIPTOR.message_types_by_name['DiscoverResponse']
_DISCOVERBATCHRESPONSE = DESCRIPTOR.message_types_by_name['DiscoverBatchResponse']
_RECOMMENDGROUPSRESPONSE = DESCRIPTOR.message_types_by_name['RecommendGroupsResponse']
_UPDATEBATCHRESPONSE = DESCRIPTOR.message_types_by_name['UpdateBatchResponse']
_FACETRESPONSE = DESCRIPTOR.message_types_by_name['FacetResponse']
_SEARCHMATRIXPAIRSRESPONSE = DESCRIPTOR.message_types_by_name['SearchMatrixPairsResponse']
_SEARCHMATRIXOFFSETSRESPONSE = DESCRIPTOR.message_types_by_name['SearchMatrixOffsetsResponse']
_FILTER = DESCRIPTOR.message_types_by_name['Filter']
_MINSHOULD = DESCRIPTOR.message_types_by_name['MinShould']
_CONDITION = DESCRIPTOR.message_types_by_name['Condition']
_ISEMPTYCONDITION = DESCRIPTOR.message_types_by_name['IsEmptyCondition']
_ISNULLCONDITION = DESCRIPTOR.message_types_by_name['IsNullCondition']
_HASIDCONDITION = DESCRIPTOR.message_types_by_name['HasIdCondition']
_HASVECTORCONDITION = DESCRIPTOR.message_types_by_name['HasVectorCondition']
_NESTEDCONDITION = DESCRIPTOR.message_types_by_name['NestedCondition']
_FIELDCONDITION = DESCRIPTOR.message_types_by_name['FieldCondition']
_MATCH = DESCRIPTOR.message_types_by_name['Match']
_REPEATEDSTRINGS = DESCRIPTOR.message_types_by_name['RepeatedStrings']
_REPEATEDINTEGERS = DESCRIPTOR.message_types_by_name['RepeatedIntegers']
_RANGE = DESCRIPTOR.message_types_by_name['Range']
_DATETIMERANGE = DESCRIPTOR.message_types_by_name['DatetimeRange']
_GEOBOUNDINGBOX = DESCRIPTOR.message_types_by_name['GeoBoundingBox']
_GEORADIUS = DESCRIPTOR.message_types_by_name['GeoRadius']
_GEOLINESTRING = DESCRIPTOR.message_types_by_name['GeoLineString']
_GEOPOLYGON = DESCRIPTOR.message_types_by_name['GeoPolygon']
_VALUESCOUNT = DESCRIPTOR.message_types_by_name['ValuesCount']
_POINTSSELECTOR = DESCRIPTOR.message_types_by_name['PointsSelector']
_POINTSIDSLIST = DESCRIPTOR.message_types_by_name['PointsIdsList']
_POINTSTRUCT = DESCRIPTOR.message_types_by_name['PointStruct']
_POINTSTRUCT_PAYLOADENTRY = _POINTSTRUCT.nested_types_by_name['PayloadEntry']
_GEOPOINT = DESCRIPTOR.message_types_by_name['GeoPoint']
_HARDWAREUSAGE = DESCRIPTOR.message_types_by_name['HardwareUsage']
WriteOrdering = _reflection.GeneratedProtocolMessageType('WriteOrdering', (_message.Message,), {
  'DESCRIPTOR' : _WRITEORDERING,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.WriteOrdering)
  })
_sym_db.RegisterMessage(WriteOrdering)

ReadConsistency = _reflection.GeneratedProtocolMessageType('ReadConsistency', (_message.Message,), {
  'DESCRIPTOR' : _READCONSISTENCY,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ReadConsistency)
  })
_sym_db.RegisterMessage(ReadConsistency)

PointId = _reflection.GeneratedProtocolMessageType('PointId', (_message.Message,), {
  'DESCRIPTOR' : _POINTID,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PointId)
  })
_sym_db.RegisterMessage(PointId)

SparseIndices = _reflection.GeneratedProtocolMessageType('SparseIndices', (_message.Message,), {
  'DESCRIPTOR' : _SPARSEINDICES,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SparseIndices)
  })
_sym_db.RegisterMessage(SparseIndices)

Document = _reflection.GeneratedProtocolMessageType('Document', (_message.Message,), {

  'OptionsEntry' : _reflection.GeneratedProtocolMessageType('OptionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _DOCUMENT_OPTIONSENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.Document.OptionsEntry)
    })
  ,
  'DESCRIPTOR' : _DOCUMENT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Document)
  })
_sym_db.RegisterMessage(Document)
_sym_db.RegisterMessage(Document.OptionsEntry)

Image = _reflection.GeneratedProtocolMessageType('Image', (_message.Message,), {

  'OptionsEntry' : _reflection.GeneratedProtocolMessageType('OptionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _IMAGE_OPTIONSENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.Image.OptionsEntry)
    })
  ,
  'DESCRIPTOR' : _IMAGE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Image)
  })
_sym_db.RegisterMessage(Image)
_sym_db.RegisterMessage(Image.OptionsEntry)

InferenceObject = _reflection.GeneratedProtocolMessageType('InferenceObject', (_message.Message,), {

  'OptionsEntry' : _reflection.GeneratedProtocolMessageType('OptionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _INFERENCEOBJECT_OPTIONSENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.InferenceObject.OptionsEntry)
    })
  ,
  'DESCRIPTOR' : _INFERENCEOBJECT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.InferenceObject)
  })
_sym_db.RegisterMessage(InferenceObject)
_sym_db.RegisterMessage(InferenceObject.OptionsEntry)

Vector = _reflection.GeneratedProtocolMessageType('Vector', (_message.Message,), {
  'DESCRIPTOR' : _VECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Vector)
  })
_sym_db.RegisterMessage(Vector)

VectorOutput = _reflection.GeneratedProtocolMessageType('VectorOutput', (_message.Message,), {
  'DESCRIPTOR' : _VECTOROUTPUT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorOutput)
  })
_sym_db.RegisterMessage(VectorOutput)

DenseVector = _reflection.GeneratedProtocolMessageType('DenseVector', (_message.Message,), {
  'DESCRIPTOR' : _DENSEVECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DenseVector)
  })
_sym_db.RegisterMessage(DenseVector)

SparseVector = _reflection.GeneratedProtocolMessageType('SparseVector', (_message.Message,), {
  'DESCRIPTOR' : _SPARSEVECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SparseVector)
  })
_sym_db.RegisterMessage(SparseVector)

MultiDenseVector = _reflection.GeneratedProtocolMessageType('MultiDenseVector', (_message.Message,), {
  'DESCRIPTOR' : _MULTIDENSEVECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.MultiDenseVector)
  })
_sym_db.RegisterMessage(MultiDenseVector)

VectorInput = _reflection.GeneratedProtocolMessageType('VectorInput', (_message.Message,), {
  'DESCRIPTOR' : _VECTORINPUT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorInput)
  })
_sym_db.RegisterMessage(VectorInput)

ShardKeySelector = _reflection.GeneratedProtocolMessageType('ShardKeySelector', (_message.Message,), {
  'DESCRIPTOR' : _SHARDKEYSELECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ShardKeySelector)
  })
_sym_db.RegisterMessage(ShardKeySelector)

UpsertPoints = _reflection.GeneratedProtocolMessageType('UpsertPoints', (_message.Message,), {
  'DESCRIPTOR' : _UPSERTPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpsertPoints)
  })
_sym_db.RegisterMessage(UpsertPoints)

DeletePoints = _reflection.GeneratedProtocolMessageType('DeletePoints', (_message.Message,), {
  'DESCRIPTOR' : _DELETEPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeletePoints)
  })
_sym_db.RegisterMessage(DeletePoints)

GetPoints = _reflection.GeneratedProtocolMessageType('GetPoints', (_message.Message,), {
  'DESCRIPTOR' : _GETPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GetPoints)
  })
_sym_db.RegisterMessage(GetPoints)

UpdatePointVectors = _reflection.GeneratedProtocolMessageType('UpdatePointVectors', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEPOINTVECTORS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdatePointVectors)
  })
_sym_db.RegisterMessage(UpdatePointVectors)

PointVectors = _reflection.GeneratedProtocolMessageType('PointVectors', (_message.Message,), {
  'DESCRIPTOR' : _POINTVECTORS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PointVectors)
  })
_sym_db.RegisterMessage(PointVectors)

DeletePointVectors = _reflection.GeneratedProtocolMessageType('DeletePointVectors', (_message.Message,), {
  'DESCRIPTOR' : _DELETEPOINTVECTORS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeletePointVectors)
  })
_sym_db.RegisterMessage(DeletePointVectors)

SetPayloadPoints = _reflection.GeneratedProtocolMessageType('SetPayloadPoints', (_message.Message,), {

  'PayloadEntry' : _reflection.GeneratedProtocolMessageType('PayloadEntry', (_message.Message,), {
    'DESCRIPTOR' : _SETPAYLOADPOINTS_PAYLOADENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.SetPayloadPoints.PayloadEntry)
    })
  ,
  'DESCRIPTOR' : _SETPAYLOADPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SetPayloadPoints)
  })
_sym_db.RegisterMessage(SetPayloadPoints)
_sym_db.RegisterMessage(SetPayloadPoints.PayloadEntry)

DeletePayloadPoints = _reflection.GeneratedProtocolMessageType('DeletePayloadPoints', (_message.Message,), {
  'DESCRIPTOR' : _DELETEPAYLOADPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeletePayloadPoints)
  })
_sym_db.RegisterMessage(DeletePayloadPoints)

ClearPayloadPoints = _reflection.GeneratedProtocolMessageType('ClearPayloadPoints', (_message.Message,), {
  'DESCRIPTOR' : _CLEARPAYLOADPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ClearPayloadPoints)
  })
_sym_db.RegisterMessage(ClearPayloadPoints)

CreateFieldIndexCollection = _reflection.GeneratedProtocolMessageType('CreateFieldIndexCollection', (_message.Message,), {
  'DESCRIPTOR' : _CREATEFIELDINDEXCOLLECTION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateFieldIndexCollection)
  })
_sym_db.RegisterMessage(CreateFieldIndexCollection)

DeleteFieldIndexCollection = _reflection.GeneratedProtocolMessageType('DeleteFieldIndexCollection', (_message.Message,), {
  'DESCRIPTOR' : _DELETEFIELDINDEXCOLLECTION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteFieldIndexCollection)
  })
_sym_db.RegisterMessage(DeleteFieldIndexCollection)

PayloadIncludeSelector = _reflection.GeneratedProtocolMessageType('PayloadIncludeSelector', (_message.Message,), {
  'DESCRIPTOR' : _PAYLOADINCLUDESELECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PayloadIncludeSelector)
  })
_sym_db.RegisterMessage(PayloadIncludeSelector)

PayloadExcludeSelector = _reflection.GeneratedProtocolMessageType('PayloadExcludeSelector', (_message.Message,), {
  'DESCRIPTOR' : _PAYLOADEXCLUDESELECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PayloadExcludeSelector)
  })
_sym_db.RegisterMessage(PayloadExcludeSelector)

WithPayloadSelector = _reflection.GeneratedProtocolMessageType('WithPayloadSelector', (_message.Message,), {
  'DESCRIPTOR' : _WITHPAYLOADSELECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.WithPayloadSelector)
  })
_sym_db.RegisterMessage(WithPayloadSelector)

NamedVectors = _reflection.GeneratedProtocolMessageType('NamedVectors', (_message.Message,), {

  'VectorsEntry' : _reflection.GeneratedProtocolMessageType('VectorsEntry', (_message.Message,), {
    'DESCRIPTOR' : _NAMEDVECTORS_VECTORSENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.NamedVectors.VectorsEntry)
    })
  ,
  'DESCRIPTOR' : _NAMEDVECTORS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.NamedVectors)
  })
_sym_db.RegisterMessage(NamedVectors)
_sym_db.RegisterMessage(NamedVectors.VectorsEntry)

NamedVectorsOutput = _reflection.GeneratedProtocolMessageType('NamedVectorsOutput', (_message.Message,), {

  'VectorsEntry' : _reflection.GeneratedProtocolMessageType('VectorsEntry', (_message.Message,), {
    'DESCRIPTOR' : _NAMEDVECTORSOUTPUT_VECTORSENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.NamedVectorsOutput.VectorsEntry)
    })
  ,
  'DESCRIPTOR' : _NAMEDVECTORSOUTPUT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.NamedVectorsOutput)
  })
_sym_db.RegisterMessage(NamedVectorsOutput)
_sym_db.RegisterMessage(NamedVectorsOutput.VectorsEntry)

Vectors = _reflection.GeneratedProtocolMessageType('Vectors', (_message.Message,), {
  'DESCRIPTOR' : _VECTORS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Vectors)
  })
_sym_db.RegisterMessage(Vectors)

VectorsOutput = _reflection.GeneratedProtocolMessageType('VectorsOutput', (_message.Message,), {
  'DESCRIPTOR' : _VECTORSOUTPUT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorsOutput)
  })
_sym_db.RegisterMessage(VectorsOutput)

VectorsSelector = _reflection.GeneratedProtocolMessageType('VectorsSelector', (_message.Message,), {
  'DESCRIPTOR' : _VECTORSSELECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorsSelector)
  })
_sym_db.RegisterMessage(VectorsSelector)

WithVectorsSelector = _reflection.GeneratedProtocolMessageType('WithVectorsSelector', (_message.Message,), {
  'DESCRIPTOR' : _WITHVECTORSSELECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.WithVectorsSelector)
  })
_sym_db.RegisterMessage(WithVectorsSelector)

QuantizationSearchParams = _reflection.GeneratedProtocolMessageType('QuantizationSearchParams', (_message.Message,), {
  'DESCRIPTOR' : _QUANTIZATIONSEARCHPARAMS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QuantizationSearchParams)
  })
_sym_db.RegisterMessage(QuantizationSearchParams)

SearchParams = _reflection.GeneratedProtocolMessageType('SearchParams', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHPARAMS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchParams)
  })
_sym_db.RegisterMessage(SearchParams)

SearchPoints = _reflection.GeneratedProtocolMessageType('SearchPoints', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchPoints)
  })
_sym_db.RegisterMessage(SearchPoints)

SearchBatchPoints = _reflection.GeneratedProtocolMessageType('SearchBatchPoints', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHBATCHPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchBatchPoints)
  })
_sym_db.RegisterMessage(SearchBatchPoints)

WithLookup = _reflection.GeneratedProtocolMessageType('WithLookup', (_message.Message,), {
  'DESCRIPTOR' : _WITHLOOKUP,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.WithLookup)
  })
_sym_db.RegisterMessage(WithLookup)

SearchPointGroups = _reflection.GeneratedProtocolMessageType('SearchPointGroups', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHPOINTGROUPS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchPointGroups)
  })
_sym_db.RegisterMessage(SearchPointGroups)

StartFrom = _reflection.GeneratedProtocolMessageType('StartFrom', (_message.Message,), {
  'DESCRIPTOR' : _STARTFROM,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.StartFrom)
  })
_sym_db.RegisterMessage(StartFrom)

OrderBy = _reflection.GeneratedProtocolMessageType('OrderBy', (_message.Message,), {
  'DESCRIPTOR' : _ORDERBY,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.OrderBy)
  })
_sym_db.RegisterMessage(OrderBy)

ScrollPoints = _reflection.GeneratedProtocolMessageType('ScrollPoints', (_message.Message,), {
  'DESCRIPTOR' : _SCROLLPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ScrollPoints)
  })
_sym_db.RegisterMessage(ScrollPoints)

LookupLocation = _reflection.GeneratedProtocolMessageType('LookupLocation', (_message.Message,), {
  'DESCRIPTOR' : _LOOKUPLOCATION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.LookupLocation)
  })
_sym_db.RegisterMessage(LookupLocation)

RecommendPoints = _reflection.GeneratedProtocolMessageType('RecommendPoints', (_message.Message,), {
  'DESCRIPTOR' : _RECOMMENDPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RecommendPoints)
  })
_sym_db.RegisterMessage(RecommendPoints)

RecommendBatchPoints = _reflection.GeneratedProtocolMessageType('RecommendBatchPoints', (_message.Message,), {
  'DESCRIPTOR' : _RECOMMENDBATCHPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RecommendBatchPoints)
  })
_sym_db.RegisterMessage(RecommendBatchPoints)

RecommendPointGroups = _reflection.GeneratedProtocolMessageType('RecommendPointGroups', (_message.Message,), {
  'DESCRIPTOR' : _RECOMMENDPOINTGROUPS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RecommendPointGroups)
  })
_sym_db.RegisterMessage(RecommendPointGroups)

TargetVector = _reflection.GeneratedProtocolMessageType('TargetVector', (_message.Message,), {
  'DESCRIPTOR' : _TARGETVECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.TargetVector)
  })
_sym_db.RegisterMessage(TargetVector)

VectorExample = _reflection.GeneratedProtocolMessageType('VectorExample', (_message.Message,), {
  'DESCRIPTOR' : _VECTOREXAMPLE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorExample)
  })
_sym_db.RegisterMessage(VectorExample)

ContextExamplePair = _reflection.GeneratedProtocolMessageType('ContextExamplePair', (_message.Message,), {
  'DESCRIPTOR' : _CONTEXTEXAMPLEPAIR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ContextExamplePair)
  })
_sym_db.RegisterMessage(ContextExamplePair)

DiscoverPoints = _reflection.GeneratedProtocolMessageType('DiscoverPoints', (_message.Message,), {
  'DESCRIPTOR' : _DISCOVERPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DiscoverPoints)
  })
_sym_db.RegisterMessage(DiscoverPoints)

DiscoverBatchPoints = _reflection.GeneratedProtocolMessageType('DiscoverBatchPoints', (_message.Message,), {
  'DESCRIPTOR' : _DISCOVERBATCHPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DiscoverBatchPoints)
  })
_sym_db.RegisterMessage(DiscoverBatchPoints)

CountPoints = _reflection.GeneratedProtocolMessageType('CountPoints', (_message.Message,), {
  'DESCRIPTOR' : _COUNTPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CountPoints)
  })
_sym_db.RegisterMessage(CountPoints)

RecommendInput = _reflection.GeneratedProtocolMessageType('RecommendInput', (_message.Message,), {
  'DESCRIPTOR' : _RECOMMENDINPUT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RecommendInput)
  })
_sym_db.RegisterMessage(RecommendInput)

ContextInputPair = _reflection.GeneratedProtocolMessageType('ContextInputPair', (_message.Message,), {
  'DESCRIPTOR' : _CONTEXTINPUTPAIR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ContextInputPair)
  })
_sym_db.RegisterMessage(ContextInputPair)

DiscoverInput = _reflection.GeneratedProtocolMessageType('DiscoverInput', (_message.Message,), {
  'DESCRIPTOR' : _DISCOVERINPUT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DiscoverInput)
  })
_sym_db.RegisterMessage(DiscoverInput)

ContextInput = _reflection.GeneratedProtocolMessageType('ContextInput', (_message.Message,), {
  'DESCRIPTOR' : _CONTEXTINPUT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ContextInput)
  })
_sym_db.RegisterMessage(ContextInput)

Formula = _reflection.GeneratedProtocolMessageType('Formula', (_message.Message,), {

  'DefaultsEntry' : _reflection.GeneratedProtocolMessageType('DefaultsEntry', (_message.Message,), {
    'DESCRIPTOR' : _FORMULA_DEFAULTSENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.Formula.DefaultsEntry)
    })
  ,
  'DESCRIPTOR' : _FORMULA,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Formula)
  })
_sym_db.RegisterMessage(Formula)
_sym_db.RegisterMessage(Formula.DefaultsEntry)

Expression = _reflection.GeneratedProtocolMessageType('Expression', (_message.Message,), {
  'DESCRIPTOR' : _EXPRESSION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Expression)
  })
_sym_db.RegisterMessage(Expression)

GeoDistance = _reflection.GeneratedProtocolMessageType('GeoDistance', (_message.Message,), {
  'DESCRIPTOR' : _GEODISTANCE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GeoDistance)
  })
_sym_db.RegisterMessage(GeoDistance)

MultExpression = _reflection.GeneratedProtocolMessageType('MultExpression', (_message.Message,), {
  'DESCRIPTOR' : _MULTEXPRESSION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.MultExpression)
  })
_sym_db.RegisterMessage(MultExpression)

SumExpression = _reflection.GeneratedProtocolMessageType('SumExpression', (_message.Message,), {
  'DESCRIPTOR' : _SUMEXPRESSION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SumExpression)
  })
_sym_db.RegisterMessage(SumExpression)

DivExpression = _reflection.GeneratedProtocolMessageType('DivExpression', (_message.Message,), {
  'DESCRIPTOR' : _DIVEXPRESSION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DivExpression)
  })
_sym_db.RegisterMessage(DivExpression)

PowExpression = _reflection.GeneratedProtocolMessageType('PowExpression', (_message.Message,), {
  'DESCRIPTOR' : _POWEXPRESSION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PowExpression)
  })
_sym_db.RegisterMessage(PowExpression)

DecayParamsExpression = _reflection.GeneratedProtocolMessageType('DecayParamsExpression', (_message.Message,), {
  'DESCRIPTOR' : _DECAYPARAMSEXPRESSION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DecayParamsExpression)
  })
_sym_db.RegisterMessage(DecayParamsExpression)

Query = _reflection.GeneratedProtocolMessageType('Query', (_message.Message,), {
  'DESCRIPTOR' : _QUERY,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Query)
  })
_sym_db.RegisterMessage(Query)

PrefetchQuery = _reflection.GeneratedProtocolMessageType('PrefetchQuery', (_message.Message,), {
  'DESCRIPTOR' : _PREFETCHQUERY,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PrefetchQuery)
  })
_sym_db.RegisterMessage(PrefetchQuery)

QueryPoints = _reflection.GeneratedProtocolMessageType('QueryPoints', (_message.Message,), {
  'DESCRIPTOR' : _QUERYPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QueryPoints)
  })
_sym_db.RegisterMessage(QueryPoints)

QueryBatchPoints = _reflection.GeneratedProtocolMessageType('QueryBatchPoints', (_message.Message,), {
  'DESCRIPTOR' : _QUERYBATCHPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QueryBatchPoints)
  })
_sym_db.RegisterMessage(QueryBatchPoints)

QueryPointGroups = _reflection.GeneratedProtocolMessageType('QueryPointGroups', (_message.Message,), {
  'DESCRIPTOR' : _QUERYPOINTGROUPS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QueryPointGroups)
  })
_sym_db.RegisterMessage(QueryPointGroups)

FacetCounts = _reflection.GeneratedProtocolMessageType('FacetCounts', (_message.Message,), {
  'DESCRIPTOR' : _FACETCOUNTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.FacetCounts)
  })
_sym_db.RegisterMessage(FacetCounts)

FacetValue = _reflection.GeneratedProtocolMessageType('FacetValue', (_message.Message,), {
  'DESCRIPTOR' : _FACETVALUE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.FacetValue)
  })
_sym_db.RegisterMessage(FacetValue)

FacetHit = _reflection.GeneratedProtocolMessageType('FacetHit', (_message.Message,), {
  'DESCRIPTOR' : _FACETHIT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.FacetHit)
  })
_sym_db.RegisterMessage(FacetHit)

SearchMatrixPoints = _reflection.GeneratedProtocolMessageType('SearchMatrixPoints', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHMATRIXPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchMatrixPoints)
  })
_sym_db.RegisterMessage(SearchMatrixPoints)

SearchMatrixPairs = _reflection.GeneratedProtocolMessageType('SearchMatrixPairs', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHMATRIXPAIRS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchMatrixPairs)
  })
_sym_db.RegisterMessage(SearchMatrixPairs)

SearchMatrixPair = _reflection.GeneratedProtocolMessageType('SearchMatrixPair', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHMATRIXPAIR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchMatrixPair)
  })
_sym_db.RegisterMessage(SearchMatrixPair)

SearchMatrixOffsets = _reflection.GeneratedProtocolMessageType('SearchMatrixOffsets', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHMATRIXOFFSETS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchMatrixOffsets)
  })
_sym_db.RegisterMessage(SearchMatrixOffsets)

PointsUpdateOperation = _reflection.GeneratedProtocolMessageType('PointsUpdateOperation', (_message.Message,), {

  'PointStructList' : _reflection.GeneratedProtocolMessageType('PointStructList', (_message.Message,), {
    'DESCRIPTOR' : _POINTSUPDATEOPERATION_POINTSTRUCTLIST,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.PointStructList)
    })
  ,

  'SetPayload' : _reflection.GeneratedProtocolMessageType('SetPayload', (_message.Message,), {

    'PayloadEntry' : _reflection.GeneratedProtocolMessageType('PayloadEntry', (_message.Message,), {
      'DESCRIPTOR' : _POINTSUPDATEOPERATION_SETPAYLOAD_PAYLOADENTRY,
      '__module__' : 'points_pb2'
      # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.SetPayload.PayloadEntry)
      })
    ,
    'DESCRIPTOR' : _POINTSUPDATEOPERATION_SETPAYLOAD,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.SetPayload)
    })
  ,

  'OverwritePayload' : _reflection.GeneratedProtocolMessageType('OverwritePayload', (_message.Message,), {

    'PayloadEntry' : _reflection.GeneratedProtocolMessageType('PayloadEntry', (_message.Message,), {
      'DESCRIPTOR' : _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD_PAYLOADENTRY,
      '__module__' : 'points_pb2'
      # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.OverwritePayload.PayloadEntry)
      })
    ,
    'DESCRIPTOR' : _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.OverwritePayload)
    })
  ,

  'DeletePayload' : _reflection.GeneratedProtocolMessageType('DeletePayload', (_message.Message,), {
    'DESCRIPTOR' : _POINTSUPDATEOPERATION_DELETEPAYLOAD,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.DeletePayload)
    })
  ,

  'UpdateVectors' : _reflection.GeneratedProtocolMessageType('UpdateVectors', (_message.Message,), {
    'DESCRIPTOR' : _POINTSUPDATEOPERATION_UPDATEVECTORS,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.UpdateVectors)
    })
  ,

  'DeleteVectors' : _reflection.GeneratedProtocolMessageType('DeleteVectors', (_message.Message,), {
    'DESCRIPTOR' : _POINTSUPDATEOPERATION_DELETEVECTORS,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.DeleteVectors)
    })
  ,

  'DeletePoints' : _reflection.GeneratedProtocolMessageType('DeletePoints', (_message.Message,), {
    'DESCRIPTOR' : _POINTSUPDATEOPERATION_DELETEPOINTS,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.DeletePoints)
    })
  ,

  'ClearPayload' : _reflection.GeneratedProtocolMessageType('ClearPayload', (_message.Message,), {
    'DESCRIPTOR' : _POINTSUPDATEOPERATION_CLEARPAYLOAD,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation.ClearPayload)
    })
  ,
  'DESCRIPTOR' : _POINTSUPDATEOPERATION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PointsUpdateOperation)
  })
_sym_db.RegisterMessage(PointsUpdateOperation)
_sym_db.RegisterMessage(PointsUpdateOperation.PointStructList)
_sym_db.RegisterMessage(PointsUpdateOperation.SetPayload)
_sym_db.RegisterMessage(PointsUpdateOperation.SetPayload.PayloadEntry)
_sym_db.RegisterMessage(PointsUpdateOperation.OverwritePayload)
_sym_db.RegisterMessage(PointsUpdateOperation.OverwritePayload.PayloadEntry)
_sym_db.RegisterMessage(PointsUpdateOperation.DeletePayload)
_sym_db.RegisterMessage(PointsUpdateOperation.UpdateVectors)
_sym_db.RegisterMessage(PointsUpdateOperation.DeleteVectors)
_sym_db.RegisterMessage(PointsUpdateOperation.DeletePoints)
_sym_db.RegisterMessage(PointsUpdateOperation.ClearPayload)

UpdateBatchPoints = _reflection.GeneratedProtocolMessageType('UpdateBatchPoints', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEBATCHPOINTS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateBatchPoints)
  })
_sym_db.RegisterMessage(UpdateBatchPoints)

PointsOperationResponse = _reflection.GeneratedProtocolMessageType('PointsOperationResponse', (_message.Message,), {
  'DESCRIPTOR' : _POINTSOPERATIONRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PointsOperationResponse)
  })
_sym_db.RegisterMessage(PointsOperationResponse)

UpdateResult = _reflection.GeneratedProtocolMessageType('UpdateResult', (_message.Message,), {
  'DESCRIPTOR' : _UPDATERESULT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateResult)
  })
_sym_db.RegisterMessage(UpdateResult)

OrderValue = _reflection.GeneratedProtocolMessageType('OrderValue', (_message.Message,), {
  'DESCRIPTOR' : _ORDERVALUE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.OrderValue)
  })
_sym_db.RegisterMessage(OrderValue)

ScoredPoint = _reflection.GeneratedProtocolMessageType('ScoredPoint', (_message.Message,), {

  'PayloadEntry' : _reflection.GeneratedProtocolMessageType('PayloadEntry', (_message.Message,), {
    'DESCRIPTOR' : _SCOREDPOINT_PAYLOADENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.ScoredPoint.PayloadEntry)
    })
  ,
  'DESCRIPTOR' : _SCOREDPOINT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ScoredPoint)
  })
_sym_db.RegisterMessage(ScoredPoint)
_sym_db.RegisterMessage(ScoredPoint.PayloadEntry)

GroupId = _reflection.GeneratedProtocolMessageType('GroupId', (_message.Message,), {
  'DESCRIPTOR' : _GROUPID,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GroupId)
  })
_sym_db.RegisterMessage(GroupId)

PointGroup = _reflection.GeneratedProtocolMessageType('PointGroup', (_message.Message,), {
  'DESCRIPTOR' : _POINTGROUP,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PointGroup)
  })
_sym_db.RegisterMessage(PointGroup)

GroupsResult = _reflection.GeneratedProtocolMessageType('GroupsResult', (_message.Message,), {
  'DESCRIPTOR' : _GROUPSRESULT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GroupsResult)
  })
_sym_db.RegisterMessage(GroupsResult)

SearchResponse = _reflection.GeneratedProtocolMessageType('SearchResponse', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchResponse)
  })
_sym_db.RegisterMessage(SearchResponse)

QueryResponse = _reflection.GeneratedProtocolMessageType('QueryResponse', (_message.Message,), {
  'DESCRIPTOR' : _QUERYRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QueryResponse)
  })
_sym_db.RegisterMessage(QueryResponse)

QueryBatchResponse = _reflection.GeneratedProtocolMessageType('QueryBatchResponse', (_message.Message,), {
  'DESCRIPTOR' : _QUERYBATCHRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QueryBatchResponse)
  })
_sym_db.RegisterMessage(QueryBatchResponse)

QueryGroupsResponse = _reflection.GeneratedProtocolMessageType('QueryGroupsResponse', (_message.Message,), {
  'DESCRIPTOR' : _QUERYGROUPSRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QueryGroupsResponse)
  })
_sym_db.RegisterMessage(QueryGroupsResponse)

BatchResult = _reflection.GeneratedProtocolMessageType('BatchResult', (_message.Message,), {
  'DESCRIPTOR' : _BATCHRESULT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.BatchResult)
  })
_sym_db.RegisterMessage(BatchResult)

SearchBatchResponse = _reflection.GeneratedProtocolMessageType('SearchBatchResponse', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHBATCHRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchBatchResponse)
  })
_sym_db.RegisterMessage(SearchBatchResponse)

SearchGroupsResponse = _reflection.GeneratedProtocolMessageType('SearchGroupsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHGROUPSRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchGroupsResponse)
  })
_sym_db.RegisterMessage(SearchGroupsResponse)

CountResponse = _reflection.GeneratedProtocolMessageType('CountResponse', (_message.Message,), {
  'DESCRIPTOR' : _COUNTRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CountResponse)
  })
_sym_db.RegisterMessage(CountResponse)

ScrollResponse = _reflection.GeneratedProtocolMessageType('ScrollResponse', (_message.Message,), {
  'DESCRIPTOR' : _SCROLLRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ScrollResponse)
  })
_sym_db.RegisterMessage(ScrollResponse)

CountResult = _reflection.GeneratedProtocolMessageType('CountResult', (_message.Message,), {
  'DESCRIPTOR' : _COUNTRESULT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CountResult)
  })
_sym_db.RegisterMessage(CountResult)

RetrievedPoint = _reflection.GeneratedProtocolMessageType('RetrievedPoint', (_message.Message,), {

  'PayloadEntry' : _reflection.GeneratedProtocolMessageType('PayloadEntry', (_message.Message,), {
    'DESCRIPTOR' : _RETRIEVEDPOINT_PAYLOADENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.RetrievedPoint.PayloadEntry)
    })
  ,
  'DESCRIPTOR' : _RETRIEVEDPOINT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RetrievedPoint)
  })
_sym_db.RegisterMessage(RetrievedPoint)
_sym_db.RegisterMessage(RetrievedPoint.PayloadEntry)

GetResponse = _reflection.GeneratedProtocolMessageType('GetResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GetResponse)
  })
_sym_db.RegisterMessage(GetResponse)

RecommendResponse = _reflection.GeneratedProtocolMessageType('RecommendResponse', (_message.Message,), {
  'DESCRIPTOR' : _RECOMMENDRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RecommendResponse)
  })
_sym_db.RegisterMessage(RecommendResponse)

RecommendBatchResponse = _reflection.GeneratedProtocolMessageType('RecommendBatchResponse', (_message.Message,), {
  'DESCRIPTOR' : _RECOMMENDBATCHRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RecommendBatchResponse)
  })
_sym_db.RegisterMessage(RecommendBatchResponse)

DiscoverResponse = _reflection.GeneratedProtocolMessageType('DiscoverResponse', (_message.Message,), {
  'DESCRIPTOR' : _DISCOVERRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DiscoverResponse)
  })
_sym_db.RegisterMessage(DiscoverResponse)

DiscoverBatchResponse = _reflection.GeneratedProtocolMessageType('DiscoverBatchResponse', (_message.Message,), {
  'DESCRIPTOR' : _DISCOVERBATCHRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DiscoverBatchResponse)
  })
_sym_db.RegisterMessage(DiscoverBatchResponse)

RecommendGroupsResponse = _reflection.GeneratedProtocolMessageType('RecommendGroupsResponse', (_message.Message,), {
  'DESCRIPTOR' : _RECOMMENDGROUPSRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RecommendGroupsResponse)
  })
_sym_db.RegisterMessage(RecommendGroupsResponse)

UpdateBatchResponse = _reflection.GeneratedProtocolMessageType('UpdateBatchResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEBATCHRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateBatchResponse)
  })
_sym_db.RegisterMessage(UpdateBatchResponse)

FacetResponse = _reflection.GeneratedProtocolMessageType('FacetResponse', (_message.Message,), {
  'DESCRIPTOR' : _FACETRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.FacetResponse)
  })
_sym_db.RegisterMessage(FacetResponse)

SearchMatrixPairsResponse = _reflection.GeneratedProtocolMessageType('SearchMatrixPairsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHMATRIXPAIRSRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchMatrixPairsResponse)
  })
_sym_db.RegisterMessage(SearchMatrixPairsResponse)

SearchMatrixOffsetsResponse = _reflection.GeneratedProtocolMessageType('SearchMatrixOffsetsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SEARCHMATRIXOFFSETSRESPONSE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SearchMatrixOffsetsResponse)
  })
_sym_db.RegisterMessage(SearchMatrixOffsetsResponse)

Filter = _reflection.GeneratedProtocolMessageType('Filter', (_message.Message,), {
  'DESCRIPTOR' : _FILTER,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Filter)
  })
_sym_db.RegisterMessage(Filter)

MinShould = _reflection.GeneratedProtocolMessageType('MinShould', (_message.Message,), {
  'DESCRIPTOR' : _MINSHOULD,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.MinShould)
  })
_sym_db.RegisterMessage(MinShould)

Condition = _reflection.GeneratedProtocolMessageType('Condition', (_message.Message,), {
  'DESCRIPTOR' : _CONDITION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Condition)
  })
_sym_db.RegisterMessage(Condition)

IsEmptyCondition = _reflection.GeneratedProtocolMessageType('IsEmptyCondition', (_message.Message,), {
  'DESCRIPTOR' : _ISEMPTYCONDITION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.IsEmptyCondition)
  })
_sym_db.RegisterMessage(IsEmptyCondition)

IsNullCondition = _reflection.GeneratedProtocolMessageType('IsNullCondition', (_message.Message,), {
  'DESCRIPTOR' : _ISNULLCONDITION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.IsNullCondition)
  })
_sym_db.RegisterMessage(IsNullCondition)

HasIdCondition = _reflection.GeneratedProtocolMessageType('HasIdCondition', (_message.Message,), {
  'DESCRIPTOR' : _HASIDCONDITION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.HasIdCondition)
  })
_sym_db.RegisterMessage(HasIdCondition)

HasVectorCondition = _reflection.GeneratedProtocolMessageType('HasVectorCondition', (_message.Message,), {
  'DESCRIPTOR' : _HASVECTORCONDITION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.HasVectorCondition)
  })
_sym_db.RegisterMessage(HasVectorCondition)

NestedCondition = _reflection.GeneratedProtocolMessageType('NestedCondition', (_message.Message,), {
  'DESCRIPTOR' : _NESTEDCONDITION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.NestedCondition)
  })
_sym_db.RegisterMessage(NestedCondition)

FieldCondition = _reflection.GeneratedProtocolMessageType('FieldCondition', (_message.Message,), {
  'DESCRIPTOR' : _FIELDCONDITION,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.FieldCondition)
  })
_sym_db.RegisterMessage(FieldCondition)

Match = _reflection.GeneratedProtocolMessageType('Match', (_message.Message,), {
  'DESCRIPTOR' : _MATCH,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Match)
  })
_sym_db.RegisterMessage(Match)

RepeatedStrings = _reflection.GeneratedProtocolMessageType('RepeatedStrings', (_message.Message,), {
  'DESCRIPTOR' : _REPEATEDSTRINGS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RepeatedStrings)
  })
_sym_db.RegisterMessage(RepeatedStrings)

RepeatedIntegers = _reflection.GeneratedProtocolMessageType('RepeatedIntegers', (_message.Message,), {
  'DESCRIPTOR' : _REPEATEDINTEGERS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RepeatedIntegers)
  })
_sym_db.RegisterMessage(RepeatedIntegers)

Range = _reflection.GeneratedProtocolMessageType('Range', (_message.Message,), {
  'DESCRIPTOR' : _RANGE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Range)
  })
_sym_db.RegisterMessage(Range)

DatetimeRange = _reflection.GeneratedProtocolMessageType('DatetimeRange', (_message.Message,), {
  'DESCRIPTOR' : _DATETIMERANGE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DatetimeRange)
  })
_sym_db.RegisterMessage(DatetimeRange)

GeoBoundingBox = _reflection.GeneratedProtocolMessageType('GeoBoundingBox', (_message.Message,), {
  'DESCRIPTOR' : _GEOBOUNDINGBOX,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GeoBoundingBox)
  })
_sym_db.RegisterMessage(GeoBoundingBox)

GeoRadius = _reflection.GeneratedProtocolMessageType('GeoRadius', (_message.Message,), {
  'DESCRIPTOR' : _GEORADIUS,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GeoRadius)
  })
_sym_db.RegisterMessage(GeoRadius)

GeoLineString = _reflection.GeneratedProtocolMessageType('GeoLineString', (_message.Message,), {
  'DESCRIPTOR' : _GEOLINESTRING,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GeoLineString)
  })
_sym_db.RegisterMessage(GeoLineString)

GeoPolygon = _reflection.GeneratedProtocolMessageType('GeoPolygon', (_message.Message,), {
  'DESCRIPTOR' : _GEOPOLYGON,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GeoPolygon)
  })
_sym_db.RegisterMessage(GeoPolygon)

ValuesCount = _reflection.GeneratedProtocolMessageType('ValuesCount', (_message.Message,), {
  'DESCRIPTOR' : _VALUESCOUNT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ValuesCount)
  })
_sym_db.RegisterMessage(ValuesCount)

PointsSelector = _reflection.GeneratedProtocolMessageType('PointsSelector', (_message.Message,), {
  'DESCRIPTOR' : _POINTSSELECTOR,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PointsSelector)
  })
_sym_db.RegisterMessage(PointsSelector)

PointsIdsList = _reflection.GeneratedProtocolMessageType('PointsIdsList', (_message.Message,), {
  'DESCRIPTOR' : _POINTSIDSLIST,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PointsIdsList)
  })
_sym_db.RegisterMessage(PointsIdsList)

PointStruct = _reflection.GeneratedProtocolMessageType('PointStruct', (_message.Message,), {

  'PayloadEntry' : _reflection.GeneratedProtocolMessageType('PayloadEntry', (_message.Message,), {
    'DESCRIPTOR' : _POINTSTRUCT_PAYLOADENTRY,
    '__module__' : 'points_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.PointStruct.PayloadEntry)
    })
  ,
  'DESCRIPTOR' : _POINTSTRUCT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PointStruct)
  })
_sym_db.RegisterMessage(PointStruct)
_sym_db.RegisterMessage(PointStruct.PayloadEntry)

GeoPoint = _reflection.GeneratedProtocolMessageType('GeoPoint', (_message.Message,), {
  'DESCRIPTOR' : _GEOPOINT,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GeoPoint)
  })
_sym_db.RegisterMessage(GeoPoint)

HardwareUsage = _reflection.GeneratedProtocolMessageType('HardwareUsage', (_message.Message,), {
  'DESCRIPTOR' : _HARDWAREUSAGE,
  '__module__' : 'points_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.HardwareUsage)
  })
_sym_db.RegisterMessage(HardwareUsage)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\252\002\022Qdrant.Client.Grpc'
  _DOCUMENT_OPTIONSENTRY._options = None
  _DOCUMENT_OPTIONSENTRY._serialized_options = b'8\001'
  _IMAGE_OPTIONSENTRY._options = None
  _IMAGE_OPTIONSENTRY._serialized_options = b'8\001'
  _INFERENCEOBJECT_OPTIONSENTRY._options = None
  _INFERENCEOBJECT_OPTIONSENTRY._serialized_options = b'8\001'
  _SETPAYLOADPOINTS_PAYLOADENTRY._options = None
  _SETPAYLOADPOINTS_PAYLOADENTRY._serialized_options = b'8\001'
  _NAMEDVECTORS_VECTORSENTRY._options = None
  _NAMEDVECTORS_VECTORSENTRY._serialized_options = b'8\001'
  _NAMEDVECTORSOUTPUT_VECTORSENTRY._options = None
  _NAMEDVECTORSOUTPUT_VECTORSENTRY._serialized_options = b'8\001'
  _FORMULA_DEFAULTSENTRY._options = None
  _FORMULA_DEFAULTSENTRY._serialized_options = b'8\001'
  _POINTSUPDATEOPERATION_SETPAYLOAD_PAYLOADENTRY._options = None
  _POINTSUPDATEOPERATION_SETPAYLOAD_PAYLOADENTRY._serialized_options = b'8\001'
  _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD_PAYLOADENTRY._options = None
  _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD_PAYLOADENTRY._serialized_options = b'8\001'
  _POINTSUPDATEOPERATION.fields_by_name['delete_deprecated']._options = None
  _POINTSUPDATEOPERATION.fields_by_name['delete_deprecated']._serialized_options = b'\030\001'
  _POINTSUPDATEOPERATION.fields_by_name['clear_payload_deprecated']._options = None
  _POINTSUPDATEOPERATION.fields_by_name['clear_payload_deprecated']._serialized_options = b'\030\001'
  _SCOREDPOINT_PAYLOADENTRY._options = None
  _SCOREDPOINT_PAYLOADENTRY._serialized_options = b'8\001'
  _RETRIEVEDPOINT_PAYLOADENTRY._options = None
  _RETRIEVEDPOINT_PAYLOADENTRY._serialized_options = b'8\001'
  _POINTSTRUCT_PAYLOADENTRY._options = None
  _POINTSTRUCT_PAYLOADENTRY._serialized_options = b'8\001'
  _WRITEORDERINGTYPE._serialized_start=26966
  _WRITEORDERINGTYPE._serialized_end=27019
  _READCONSISTENCYTYPE._serialized_start=27021
  _READCONSISTENCYTYPE._serialized_end=27077
  _FIELDTYPE._serialized_start=27080
  _FIELDTYPE._serialized_end=27253
  _DIRECTION._serialized_start=27255
  _DIRECTION._serialized_end=27285
  _RECOMMENDSTRATEGY._serialized_start=27287
  _RECOMMENDSTRATEGY._serialized_end=27355
  _FUSION._serialized_start=27357
  _FUSION._serialized_end=27384
  _SAMPLE._serialized_start=27386
  _SAMPLE._serialized_end=27406
  _UPDATESTATUS._serialized_start=27408
  _UPDATESTATUS._serialized_end=27499
  _WRITEORDERING._serialized_start=97
  _WRITEORDERING._serialized_end=153
  _READCONSISTENCY._serialized_start=155
  _READCONSISTENCY._serialized_end=244
  _POINTID._serialized_start=246
  _POINTID._serialized_end=306
  _SPARSEINDICES._serialized_start=308
  _SPARSEINDICES._serialized_end=337
  _DOCUMENT._serialized_start=340
  _DOCUMENT._serialized_end=490
  _DOCUMENT_OPTIONSENTRY._serialized_start=429
  _DOCUMENT_OPTIONSENTRY._serialized_end=490
  _IMAGE._serialized_start=493
  _IMAGE._serialized_end=653
  _IMAGE_OPTIONSENTRY._serialized_start=429
  _IMAGE_OPTIONSENTRY._serialized_end=490
  _INFERENCEOBJECT._serialized_start=656
  _INFERENCEOBJECT._serialized_end=837
  _INFERENCEOBJECT_OPTIONSENTRY._serialized_start=429
  _INFERENCEOBJECT_OPTIONSENTRY._serialized_end=490
  _VECTOR._serialized_start=840
  _VECTOR._serialized_end=1215
  _VECTOROUTPUT._serialized_start=1218
  _VECTOROUTPUT._serialized_end=1486
  _DENSEVECTOR._serialized_start=1488
  _DENSEVECTOR._serialized_end=1515
  _SPARSEVECTOR._serialized_start=1517
  _SPARSEVECTOR._serialized_end=1564
  _MULTIDENSEVECTOR._serialized_start=1566
  _MULTIDENSEVECTOR._serialized_end=1622
  _VECTORINPUT._serialized_start=1625
  _VECTORINPUT._serialized_end=1920
  _SHARDKEYSELECTOR._serialized_start=1922
  _SHARDKEYSELECTOR._serialized_end=1978
  _UPSERTPOINTS._serialized_start=1981
  _UPSERTPOINTS._serialized_end=2226
  _DELETEPOINTS._serialized_start=2229
  _DELETEPOINTS._serialized_end=2477
  _GETPOINTS._serialized_start=2480
  _GETPOINTS._serialized_end=2869
  _UPDATEPOINTVECTORS._serialized_start=2872
  _UPDATEPOINTVECTORS._serialized_end=3124
  _POINTVECTORS._serialized_start=3126
  _POINTVECTORS._serialized_end=3203
  _DELETEPOINTVECTORS._serialized_start=3206
  _DELETEPOINTVECTORS._serialized_end=3511
  _SETPAYLOADPOINTS._serialized_start=3514
  _SETPAYLOADPOINTS._serialized_end=3951
  _SETPAYLOADPOINTS_PAYLOADENTRY._serialized_start=3811
  _SETPAYLOADPOINTS_PAYLOADENTRY._serialized_end=3872
  _DELETEPAYLOADPOINTS._serialized_start=3954
  _DELETEPAYLOADPOINTS._serialized_end=4263
  _CLEARPAYLOADPOINTS._serialized_start=4266
  _CLEARPAYLOADPOINTS._serialized_end=4520
  _CREATEFIELDINDEXCOLLECTION._serialized_start=4523
  _CREATEFIELDINDEXCOLLECTION._serialized_end=4826
  _DELETEFIELDINDEXCOLLECTION._serialized_start=4829
  _DELETEFIELDINDEXCOLLECTION._serialized_end=4989
  _PAYLOADINCLUDESELECTOR._serialized_start=4991
  _PAYLOADINCLUDESELECTOR._serialized_end=5031
  _PAYLOADEXCLUDESELECTOR._serialized_start=5033
  _PAYLOADEXCLUDESELECTOR._serialized_end=5073
  _WITHPAYLOADSELECTOR._serialized_start=5076
  _WITHPAYLOADSELECTOR._serialized_end=5237
  _NAMEDVECTORS._serialized_start=5240
  _NAMEDVECTORS._serialized_end=5370
  _NAMEDVECTORS_VECTORSENTRY._serialized_start=5308
  _NAMEDVECTORS_VECTORSENTRY._serialized_end=5370
  _NAMEDVECTORSOUTPUT._serialized_start=5373
  _NAMEDVECTORSOUTPUT._serialized_end=5521
  _NAMEDVECTORSOUTPUT_VECTORSENTRY._serialized_start=5453
  _NAMEDVECTORSOUTPUT_VECTORSENTRY._serialized_end=5521
  _VECTORS._serialized_start=5523
  _VECTORS._serialized_end=5626
  _VECTORSOUTPUT._serialized_start=5628
  _VECTORSOUTPUT._serialized_end=5749
  _VECTORSSELECTOR._serialized_start=5751
  _VECTORSSELECTOR._serialized_end=5783
  _WITHVECTORSSELECTOR._serialized_start=5785
  _WITHVECTORSSELECTOR._serialized_end=5888
  _QUANTIZATIONSEARCHPARAMS._serialized_start=5891
  _QUANTIZATIONSEARCHPARAMS._serialized_end=6027
  _SEARCHPARAMS._serialized_start=6030
  _SEARCHPARAMS._serialized_end=6230
  _SEARCHPOINTS._serialized_start=6233
  _SEARCHPOINTS._serialized_end=6891
  _SEARCHBATCHPOINTS._serialized_start=6894
  _SEARCHBATCHPOINTS._serialized_end=7094
  _WITHLOOKUP._serialized_start=7097
  _WITHLOOKUP._serialized_end=7275
  _SEARCHPOINTGROUPS._serialized_start=7278
  _SEARCHPOINTGROUPS._serialized_end=8003
  _STARTFROM._serialized_start=8005
  _STARTFROM._serialized_end=8130
  _ORDERBY._serialized_start=8133
  _ORDERBY._serialized_end=8271
  _SCROLLPOINTS._serialized_start=8274
  _SCROLLPOINTS._serialized_end=8800
  _LOOKUPLOCATION._serialized_start=8803
  _LOOKUPLOCATION._serialized_end=8968
  _RECOMMENDPOINTS._serialized_start=8971
  _RECOMMENDPOINTS._serialized_end=9816
  _RECOMMENDBATCHPOINTS._serialized_start=9819
  _RECOMMENDBATCHPOINTS._serialized_end=10028
  _RECOMMENDPOINTGROUPS._serialized_start=10031
  _RECOMMENDPOINTGROUPS._serialized_end=10943
  _TARGETVECTOR._serialized_start=10945
  _TARGETVECTOR._serialized_end=11010
  _VECTOREXAMPLE._serialized_start=11012
  _VECTOREXAMPLE._serialized_end=11103
  _CONTEXTEXAMPLEPAIR._serialized_start=11105
  _CONTEXTEXAMPLEPAIR._serialized_end=11207
  _DISCOVERPOINTS._serialized_start=11210
  _DISCOVERPOINTS._serialized_end=11864
  _DISCOVERBATCHPOINTS._serialized_start=11867
  _DISCOVERBATCHPOINTS._serialized_end=12073
  _COUNTPOINTS._serialized_start=12076
  _COUNTPOINTS._serialized_end=12369
  _RECOMMENDINPUT._serialized_start=12372
  _RECOMMENDINPUT._serialized_end=12529
  _CONTEXTINPUTPAIR._serialized_start=12531
  _CONTEXTINPUTPAIR._serialized_end=12627
  _DISCOVERINPUT._serialized_start=12629
  _DISCOVERINPUT._serialized_end=12720
  _CONTEXTINPUT._serialized_start=12722
  _CONTEXTINPUT._serialized_end=12777
  _FORMULA._serialized_start=12780
  _FORMULA._serialized_end=12942
  _FORMULA_DEFAULTSENTRY._serialized_start=12880
  _FORMULA_DEFAULTSENTRY._serialized_end=12942
  _EXPRESSION._serialized_start=12945
  _EXPRESSION._serialized_end=13661
  _GEODISTANCE._serialized_start=13663
  _GEODISTANCE._serialized_end=13722
  _MULTEXPRESSION._serialized_start=13724
  _MULTEXPRESSION._serialized_end=13774
  _SUMEXPRESSION._serialized_start=13776
  _SUMEXPRESSION._serialized_end=13824
  _DIVEXPRESSION._serialized_start=13827
  _DIVEXPRESSION._serialized_end=13961
  _POWEXPRESSION._serialized_start=13963
  _POWEXPRESSION._serialized_end=14050
  _DECAYPARAMSEXPRESSION._serialized_start=14053
  _DECAYPARAMSEXPRESSION._serialized_end=14225
  _QUERY._serialized_start=14228
  _QUERY._serialized_end=14556
  _PREFETCHQUERY._serialized_start=14559
  _PREFETCHQUERY._serialized_end=14938
  _QUERYPOINTS._serialized_start=14941
  _QUERYPOINTS._serialized_end=15714
  _QUERYBATCHPOINTS._serialized_start=15717
  _QUERYBATCHPOINTS._serialized_end=15914
  _QUERYPOINTGROUPS._serialized_start=15917
  _QUERYPOINTGROUPS._serialized_end=16761
  _FACETCOUNTS._serialized_start=16764
  _FACETCOUNTS._serialized_end=17116
  _FACETVALUE._serialized_start=17118
  _FACETVALUE._serialized_end=17212
  _FACETHIT._serialized_start=17214
  _FACETHIT._serialized_end=17274
  _SEARCHMATRIXPOINTS._serialized_start=17277
  _SEARCHMATRIXPOINTS._serialized_end=17655
  _SEARCHMATRIXPAIRS._serialized_start=17657
  _SEARCHMATRIXPAIRS._serialized_end=17717
  _SEARCHMATRIXPAIR._serialized_start=17719
  _SEARCHMATRIXPAIR._serialized_end=17808
  _SEARCHMATRIXOFFSETS._serialized_start=17810
  _SEARCHMATRIXOFFSETS._serialized_end=17919
  _POINTSUPDATEOPERATION._serialized_start=17922
  _POINTSUPDATEOPERATION._serialized_end=20247
  _POINTSUPDATEOPERATION_POINTSTRUCTLIST._serialized_start=18627
  _POINTSUPDATEOPERATION_POINTSTRUCTLIST._serialized_end=18763
  _POINTSUPDATEOPERATION_SETPAYLOAD._serialized_start=18766
  _POINTSUPDATEOPERATION_SETPAYLOAD._serialized_end=19095
  _POINTSUPDATEOPERATION_SETPAYLOAD_PAYLOADENTRY._serialized_start=3811
  _POINTSUPDATEOPERATION_SETPAYLOAD_PAYLOADENTRY._serialized_end=3872
  _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD._serialized_start=19098
  _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD._serialized_end=19439
  _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD_PAYLOADENTRY._serialized_start=3811
  _POINTSUPDATEOPERATION_OVERWRITEPAYLOAD_PAYLOADENTRY._serialized_end=3872
  _POINTSUPDATEOPERATION_DELETEPAYLOAD._serialized_start=19442
  _POINTSUPDATEOPERATION_DELETEPAYLOAD._serialized_end=19627
  _POINTSUPDATEOPERATION_UPDATEVECTORS._serialized_start=19630
  _POINTSUPDATEOPERATION_UPDATEVECTORS._serialized_end=19765
  _POINTSUPDATEOPERATION_DELETEVECTORS._serialized_start=19768
  _POINTSUPDATEOPERATION_DELETEVECTORS._serialized_end=19956
  _POINTSUPDATEOPERATION_DELETEPOINTS._serialized_start=19959
  _POINTSUPDATEOPERATION_DELETEPOINTS._serialized_end=20095
  _POINTSUPDATEOPERATION_CLEARPAYLOAD._serialized_start=20098
  _POINTSUPDATEOPERATION_CLEARPAYLOAD._serialized_end=20234
  _UPDATEBATCHPOINTS._serialized_start=20250
  _UPDATEBATCHPOINTS._serialized_end=20432
  _POINTSOPERATIONRESPONSE._serialized_start=20435
  _POINTSOPERATIONRESPONSE._serialized_end=20565
  _UPDATERESULT._serialized_start=20567
  _UPDATERESULT._serialized_end=20663
  _ORDERVALUE._serialized_start=20665
  _ORDERVALUE._serialized_end=20720
  _SCOREDPOINT._serialized_start=20723
  _SCOREDPOINT._serialized_end=21092
  _SCOREDPOINT_PAYLOADENTRY._serialized_start=3811
  _SCOREDPOINT_PAYLOADENTRY._serialized_end=3872
  _GROUPID._serialized_start=21094
  _GROUPID._serialized_end=21186
  _POINTGROUP._serialized_start=21188
  _POINTGROUP._serialized_end=21304
  _GROUPSRESULT._serialized_start=21306
  _GROUPSRESULT._serialized_end=21356
  _SEARCHRESPONSE._serialized_start=21358
  _SEARCHRESPONSE._serialized_end=21478
  _QUERYRESPONSE._serialized_start=21480
  _QUERYRESPONSE._serialized_end=21599
  _QUERYBATCHRESPONSE._serialized_start=21601
  _QUERYBATCHRESPONSE._serialized_end=21725
  _QUERYGROUPSRESPONSE._serialized_start=21727
  _QUERYGROUPSRESPONSE._serialized_end=21853
  _BATCHRESULT._serialized_start=21855
  _BATCHRESULT._serialized_end=21905
  _SEARCHBATCHRESPONSE._serialized_start=21907
  _SEARCHBATCHRESPONSE._serialized_end=22032
  _SEARCHGROUPSRESPONSE._serialized_start=22034
  _SEARCHGROUPSRESPONSE._serialized_end=22161
  _COUNTRESPONSE._serialized_start=22163
  _COUNTRESPONSE._serialized_end=22282
  _SCROLLRESPONSE._serialized_start=22285
  _SCROLLRESPONSE._serialized_end=22477
  _COUNTRESULT._serialized_start=22479
  _COUNTRESULT._serialized_end=22507
  _RETRIEVEDPOINT._serialized_start=22510
  _RETRIEVEDPOINT._serialized_end=22853
  _RETRIEVEDPOINT_PAYLOADENTRY._serialized_start=3811
  _RETRIEVEDPOINT_PAYLOADENTRY._serialized_end=3872
  _GETRESPONSE._serialized_start=22855
  _GETRESPONSE._serialized_end=22975
  _RECOMMENDRESPONSE._serialized_start=22977
  _RECOMMENDRESPONSE._serialized_end=23100
  _RECOMMENDBATCHRESPONSE._serialized_start=23103
  _RECOMMENDBATCHRESPONSE._serialized_end=23231
  _DISCOVERRESPONSE._serialized_start=23233
  _DISCOVERRESPONSE._serialized_end=23355
  _DISCOVERBATCHRESPONSE._serialized_start=23357
  _DISCOVERBATCHRESPONSE._serialized_end=23484
  _RECOMMENDGROUPSRESPONSE._serialized_start=23487
  _RECOMMENDGROUPSRESPONSE._serialized_end=23617
  _UPDATEBATCHRESPONSE._serialized_start=23619
  _UPDATEBATCHRESPONSE._serialized_end=23692
  _FACETRESPONSE._serialized_start=23694
  _FACETRESPONSE._serialized_end=23755
  _SEARCHMATRIXPAIRSRESPONSE._serialized_start=23758
  _SEARCHMATRIXPAIRSRESPONSE._serialized_end=23895
  _SEARCHMATRIXOFFSETSRESPONSE._serialized_start=23898
  _SEARCHMATRIXOFFSETSRESPONSE._serialized_end=24039
  _FILTER._serialized_start=24042
  _FILTER._serialized_end=24214
  _MINSHOULD._serialized_start=24216
  _MINSHOULD._serialized_end=24285
  _CONDITION._serialized_start=24288
  _CONDITION._serialized_end=24619
  _ISEMPTYCONDITION._serialized_start=24621
  _ISEMPTYCONDITION._serialized_end=24652
  _ISNULLCONDITION._serialized_start=24654
  _ISNULLCONDITION._serialized_end=24684
  _HASIDCONDITION._serialized_start=24686
  _HASIDCONDITION._serialized_end=24735
  _HASVECTORCONDITION._serialized_start=24737
  _HASVECTORCONDITION._serialized_end=24777
  _NESTEDCONDITION._serialized_start=24779
  _NESTEDCONDITION._serialized_end=24841
  _FIELDCONDITION._serialized_start=24844
  _FIELDCONDITION._serialized_end=25223
  _MATCH._serialized_start=25226
  _MATCH._serialized_end=25517
  _REPEATEDSTRINGS._serialized_start=25519
  _REPEATEDSTRINGS._serialized_end=25553
  _REPEATEDINTEGERS._serialized_start=25555
  _REPEATEDINTEGERS._serialized_end=25591
  _RANGE._serialized_start=25593
  _RANGE._serialized_end=25700
  _DATETIMERANGE._serialized_start=25703
  _DATETIMERANGE._serialized_end=25930
  _GEOBOUNDINGBOX._serialized_start=25932
  _GEOBOUNDINGBOX._serialized_end=26024
  _GEORADIUS._serialized_start=26026
  _GEORADIUS._serialized_end=26087
  _GEOLINESTRING._serialized_start=26089
  _GEOLINESTRING._serialized_end=26138
  _GEOPOLYGON._serialized_start=26140
  _GEOPOLYGON._serialized_end=26235
  _VALUESCOUNT._serialized_start=26237
  _VALUESCOUNT._serialized_end=26350
  _POINTSSELECTOR._serialized_start=26352
  _POINTSSELECTOR._serialized_end=26469
  _POINTSIDSLIST._serialized_start=26471
  _POINTSIDSLIST._serialized_end=26516
  _POINTSTRUCT._serialized_start=26519
  _POINTSTRUCT._serialized_end=26732
  _POINTSTRUCT_PAYLOADENTRY._serialized_start=3811
  _POINTSTRUCT_PAYLOADENTRY._serialized_end=3872
  _GEOPOINT._serialized_start=26734
  _GEOPOINT._serialized_end=26770
  _HARDWAREUSAGE._serialized_start=26773
  _HARDWAREUSAGE._serialized_end=26964
# @@protoc_insertion_point(module_scope)
