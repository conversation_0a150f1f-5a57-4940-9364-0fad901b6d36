import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:soul_reflector_app/core/services/tts_service.dart';
import 'package:soul_reflector_app/core/config/theme_config.dart';
import 'package:soul_reflector_app/core/constants/app_strings.dart';
import 'package:soul_reflector_app/routes/app_routes.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // ⬅️ Load environment variables
  await dotenv.load();

  // Initialize Text-to-Speech
  await TtsService.initTts();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppStrings.appName,
      theme: ThemeConfig.lightTheme, // ✅ Using shared ThemeConfig
      darkTheme: ThemeConfig.darkTheme, // ✅ Consistent dark mode
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      initialRoute: AppRoutes.chat,
      onGenerateRoute: AppRoutes.generateRoute,
    );
  }
}
