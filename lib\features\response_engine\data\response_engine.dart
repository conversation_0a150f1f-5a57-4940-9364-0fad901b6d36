// 📁 lib/features/response_engine/data/response_engine.dart

import 'package:soul_reflector_app/core/services/ai_service.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_model.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_matcher.dart';
import 'package:soul_reflector_app/features/prompts/data/prompt_style_enum.dart';
import 'package:soul_reflector_app/features/response_engine/model/response_origin.dart';
import 'package:soul_reflector_app/features/templates/template_matcher.dart';

class ResponseEngineResult {
  final String content;
  final ResponseOrigin source;
  final PromptModel? promptUsed;

  ResponseEngineResult({
    required this.content,
    required this.source,
    this.promptUsed,
  });
}

class ResponseEngine {
  /// Main logic to decide where the response should come from
  static Future<ResponseEngineResult> generate({
    required String userInput,
    required PromptStyle tone,
  }) async {
    // 1. Check if there's a pre-written template match
    final templateMatch = await TemplateMatcher.matchTemplate(userInput);
    if (templateMatch != null) {
      return ResponseEngineResult(
        content: templateMatch,
        source: ResponseOrigin.template,
        promptUsed: null,
      );
    }

    // 2. Load prompt if matched
    final matchedPrompt = await PromptMatcher.getBestPrompt(
      userInput: userInput,
      style: tone,
    );

    final promptText =
        (matchedPrompt != null && matchedPrompt.promptTexts.isNotEmpty)
            ? matchedPrompt.promptTexts[DateTime.now().millisecondsSinceEpoch %
                matchedPrompt.promptTexts.length]
            : '''
You are a mystical, poetic, meditative voice. Answer like a gentle spiritual teacher from no tradition. Invite awareness, not belief.
''';

    // 3. Call GPT
    final aiReply = await AiService.getReflectiveResponse(
      userInput: userInput,
      systemPrompt: promptText,
    );

    return ResponseEngineResult(
      content: aiReply,
      source: ResponseOrigin.aiGenerated,
      promptUsed: matchedPrompt,
    );
  }
}
