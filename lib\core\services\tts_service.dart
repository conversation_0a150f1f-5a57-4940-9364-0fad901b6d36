import 'package:flutter_tts/flutter_tts.dart';

class TtsService {
  static final FlutterTts _flutterTts = FlutterTts();

  static Future<void> initTts() async {
    await _flutterTts.setLanguage("en-US");
    await _flutterTts.setPitch(1.0);
    await _flutterTts.setSpeechRate(0.45);
    await _flutterTts.setVolume(1.0);
  }

  static Future<void> speak(String text) async {
    await _flutterTts.stop(); // stop any existing speech
    await _flutterTts.speak(text);
  }

  static Future<void> stop() async {
    await _flutterTts.stop();
  }

  static Future<void> setSlowMysticMode() async {
    await _flutterTts.setPitch(0.8);
    await _flutterTts.setSpeechRate(0.35);
  }
}
